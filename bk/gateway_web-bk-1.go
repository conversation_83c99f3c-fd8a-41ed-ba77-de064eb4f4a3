package main

import (
	"context"
	"encoding/json"
	"fmt"
	"html/template"
	"io"
	"log"
	"net"
	"net/http"
	"os"
	"os/exec"
	"os/signal"
	"path/filepath"
	"runtime"
	"sort"
	"strings"
	"sync"
	"sync/atomic"
	"syscall"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
	"github.com/tarm/serial"
	"gopkg.in/yaml.v2"
)

// Config 定义 RK3566 设备配置文件结构
type Config struct {
	Serial struct {
		MasterDevice string `yaml:"master_device" json:"master_device"`
		SlaveDevice  string `yaml:"slave_device" json:"slave_device"`
		BaudRate     uint32 `yaml:"baud_rate" json:"baud_rate"`
		DataBits     int    `yaml:"data_bits" json:"data_bits"`
		Parity       string `yaml:"parity" json:"parity"`
		StopBits     int    `yaml:"stop_bits" json:"stop_bits"`
		TimeoutMs    int    `yaml:"timeout_ms" json:"timeout_ms"`
		Mode         string `yaml:"mode" json:"mode"`
	} `yaml:"serial" json:"serial"`
	Network struct {
		TCPPort      int    `yaml:"tcp_port" json:"tcp_port"`
		WebPort      int    `yaml:"web_port" json:"web_port"`
		MaxClients   int    `yaml:"max_clients" json:"max_clients"`
		MqttBroker   string `yaml:"mqtt_broker" json:"mqtt_broker"`
		DeviceID     string `yaml:"device_id" json:"device_id"`
		MqttUsername string `yaml:"mqtt_username" json:"mqtt_username"`
		MqttPassword string `yaml:"mqtt_password" json:"mqtt_password"`
	} `yaml:"network" json:"network"`
	Modbus struct {
		MaxADULength int `yaml:"max_adu_length" json:"max_adu_length"`
	} `yaml:"modbus" json:"modbus"`
	Runtime struct {
		QueueSize int `yaml:"queue_size" json:"queue_size"`
	} `yaml:"runtime" json:"runtime"`
}

// SystemStatus 系统状态结构
type SystemStatus struct {
	StartTime       time.Time `json:"start_time"`
	Uptime          string    `json:"uptime"`
	TCPConnections  int32     `json:"tcp_connections"`
	MQTTConnected   bool      `json:"mqtt_connected"`
	SerialMasterOK  bool      `json:"serial_master_ok"`
	SerialSlaveOK   bool      `json:"serial_slave_ok"`
	TotalRequests   int64     `json:"total_requests"`
	SuccessRequests int64     `json:"success_requests"`
	ErrorRequests   int64     `json:"error_requests"`
	QueueLength     int       `json:"queue_length"`
	LastActivity    time.Time `json:"last_activity"`
	CPUUsage        float64   `json:"cpu_usage"`
	MemoryUsage     int64     `json:"memory_usage"`
}

var (
	config         Config
	running        = true
	masterFd       *serial.Port
	slaveFd        *serial.Port
	serverSock     net.Listener
	mqttClient     mqtt.Client
	queue          chan Request
	wg             sync.WaitGroup
	pool           sync.Pool
	slaveMutex     sync.Mutex
	configFilePath string // 保存加载的配置文件路径
	webServer      *http.Server

	// 状态监控变量
	systemStatus    SystemStatus
	statusMutex     sync.RWMutex
	tcpConnCount    int32
	totalRequests   int64
	successRequests int64
	errorRequests   int64
	startTime       time.Time

	// 日志管理
	logBuffer         []string
	logMutex          sync.RWMutex
	maxLogLines       = 1000
	logLevel          = "INFO" // DEBUG, INFO, WARN, ERROR
	logFile           *os.File
	logDir            = "/root/modbus-gateway/logs"
	maxLogSize        = int64(10 * 1024 * 1024) // 10MB
	maxLogFiles       = 10                      // 保留10个日志文件
	currentLogSize    int64
	logRotateByTime   = false // 是否按时间轮转
	logRotateInterval = 24    // 小时，按时间轮转的间隔
)

// recoverFromPanic 通用的panic恢复函数
func recoverFromPanic(funcName string) {
	if r := recover(); r != nil {
		errMsg := fmt.Sprintf("[PANIC恢复] %s 发生panic: %v", funcName, r)
		addLogWithLevel("ERROR", errMsg)

		// 记录堆栈信息
		buf := make([]byte, 4096)
		n := runtime.Stack(buf, false)
		stackMsg := fmt.Sprintf("[PANIC堆栈] %s:\n%s", funcName, string(buf[:n]))
		addLogWithLevel("ERROR", stackMsg)

		atomic.AddInt64(&errorRequests, 1)
	}
}

type RequestType int

const (
	TCP_REQ RequestType = iota
	RTU_REQ
	ASCII_REQ
	MQTT_REQ
)

// Request 定义请求结构
type Request struct {
	Type       RequestType
	ClientSock net.Conn
	Data       []byte
}

// loadConfig 加载配置文件
func loadConfig() error {
	// 默认配置
	config = Config{
		Serial: struct {
			MasterDevice string `yaml:"master_device" json:"master_device"`
			SlaveDevice  string `yaml:"slave_device" json:"slave_device"`
			BaudRate     uint32 `yaml:"baud_rate" json:"baud_rate"`
			DataBits     int    `yaml:"data_bits" json:"data_bits"`
			Parity       string `yaml:"parity" json:"parity"`
			StopBits     int    `yaml:"stop_bits" json:"stop_bits"`
			TimeoutMs    int    `yaml:"timeout_ms" json:"timeout_ms"`
			Mode         string `yaml:"mode" json:"mode"`
		}{
			MasterDevice: "/dev/ttyS3",
			SlaveDevice:  "/dev/ttyS4",
			BaudRate:     115200,
			DataBits:     8,
			Parity:       "N",
			StopBits:     1,
			TimeoutMs:    1000,
			Mode:         "RTU",
		},
		Network: struct {
			TCPPort      int    `yaml:"tcp_port" json:"tcp_port"`
			WebPort      int    `yaml:"web_port" json:"web_port"`
			MaxClients   int    `yaml:"max_clients" json:"max_clients"`
			MqttBroker   string `yaml:"mqtt_broker" json:"mqtt_broker"`
			DeviceID     string `yaml:"device_id" json:"device_id"`
			MqttUsername string `yaml:"mqtt_username" json:"mqtt_username"`
			MqttPassword string `yaml:"mqtt_password" json:"mqtt_password"`
		}{
			TCPPort:      1502,
			WebPort:      8080,
			MaxClients:   5,
			MqttBroker:   "tcp://localhost:1883",
			DeviceID:     "rk3566_001",
			MqttUsername: "rk_user",
			MqttPassword: "rk_pass",
		},
		Modbus: struct {
			MaxADULength int `yaml:"max_adu_length" json:"max_adu_length"`
		}{
			MaxADULength: 256,
		},
		Runtime: struct {
			QueueSize int `yaml:"queue_size" json:"queue_size"`
		}{
			QueueSize: 500,
		},
	}

	// 检查命令行参数
	var configFile string
	if len(os.Args) > 1 {
		configFile = os.Args[1] // 第一个参数作为配置文件路径
	} else {
		// 未指定参数时，尝试默认路径
		defaultPaths := []string{"./config_web.yaml", "./config.yaml", "/etc/modbus-gateway/config.yaml"}
		for _, path := range defaultPaths {
			if _, err := os.Stat(path); err == nil {
				configFile = path
				break
			}
		}
	}

	// 如果找到配置文件，尝试加载
	if configFile != "" {
		data, err := os.ReadFile(configFile)
		if err != nil {
			log.Printf("警告: 无法读取配置文件 %s，使用默认配置: %v", configFile, err)
		} else {
			err = yaml.Unmarshal(data, &config)
			if err != nil {
				return fmt.Errorf("解析配置文件 %s 失败: %v", configFile, err)
			}
			addLogWithLevel("INFO", fmt.Sprintf("从 %s 加载配置成功", configFile))
			configFilePath = configFile // 保存加载的路径
		}
	} else {
		addLogWithLevel("INFO", "未指定或找到配置文件，使用默认配置")
		configFilePath = "./config_web.yaml" // 默认保存路径
	}

	// 环境变量覆盖
	if env := os.Getenv("MASTER_DEV"); env != "" {
		config.Serial.MasterDevice = env
	}
	if env := os.Getenv("SLAVE_DEV"); env != "" {
		config.Serial.SlaveDevice = env
	}
	if env := os.Getenv("MODE"); env != "" {
		config.Serial.Mode = env
	}
	if env := os.Getenv("MQTT_BROKER"); env != "" {
		config.Network.MqttBroker = env
	}
	if env := os.Getenv("DEVICE_ID"); env != "" {
		config.Network.DeviceID = env
	}
	if env := os.Getenv("MQTT_USERNAME"); env != "" {
		config.Network.MqttUsername = env
	}
	if env := os.Getenv("MQTT_PASSWORD"); env != "" {
		config.Network.MqttPassword = env
	}

	return nil
}

// calculateCRC 计算CRC16校验码
func calculateCRC(buffer []byte) uint16 {
	crc := uint16(0xFFFF)
	for _, b := range buffer {
		crc ^= uint16(b)
		for i := 0; i < 8; i++ {
			if crc&1 != 0 {
				crc = (crc >> 1) ^ 0xA001
			} else {
				crc >>= 1
			}
		}
	}
	return crc
}

// initSerial 初始化串口
func initSerial(device string) (*serial.Port, error) {
	c := &serial.Config{
		Name:        device,
		Baud:        int(config.Serial.BaudRate),
		Size:        byte(config.Serial.DataBits),
		StopBits:    serial.StopBits(config.Serial.StopBits),
		ReadTimeout: time.Duration(config.Serial.TimeoutMs) * time.Millisecond,
	}

	switch config.Serial.Parity {
	case "N":
		c.Parity = serial.ParityNone
	case "E":
		c.Parity = serial.ParityEven
	case "O":
		c.Parity = serial.ParityOdd
	default:
		c.Parity = serial.ParityNone
	}

	port, err := serial.OpenPort(c)
	if err != nil {
		addLogWithLevel("ERROR", fmt.Sprintf("打开串口 %s 失败: %v", device, err))
		return nil, fmt.Errorf("打开串口 %s 失败: %v", device, err)
	}

	logMsg := fmt.Sprintf("串口 %s 初始化成功 (波特率: %d, 数据位: %d, 校验: %s, 停止位: %d)",
		device, config.Serial.BaudRate, config.Serial.DataBits, config.Serial.Parity, config.Serial.StopBits)
	log.Print(logMsg)
	addLogWithLevel("INFO", logMsg)
	return port, nil
}

// flushSerial 清空串口缓冲区
func flushSerial(port *serial.Port) {
	buffer := make([]byte, 1024)
	for {
		n, err := port.Read(buffer)
		if err != nil || n == 0 {
			break
		}
	}
}

// readMasterRequest 从Master读取请求
func readMasterRequest(r io.Reader, deadline time.Time) ([]byte, error) {
	buffer := make([]byte, config.Modbus.MaxADULength)
	totalRead := 0

	for totalRead < 8 {
		if !deadline.IsZero() && time.Now().After(deadline) {
			return nil, fmt.Errorf("读取超时")
		}
		n, err := r.Read(buffer[totalRead:])
		if err != nil {
			return nil, err
		}
		totalRead += n
	}

	if totalRead < 8 {
		return nil, fmt.Errorf("数据包太短")
	}

	var expectedLength int
	if config.Serial.Mode == "RTU" {
		functionCode := buffer[1]
		switch functionCode {
		case 0x01, 0x02, 0x03, 0x04:
			expectedLength = 8
		case 0x05, 0x06:
			expectedLength = 8
		case 0x0F, 0x10:
			if totalRead >= 7 {
				byteCount := int(buffer[6])
				expectedLength = 9 + byteCount
			} else {
				expectedLength = 9
			}
		default:
			expectedLength = 8
		}
	} else {
		expectedLength = totalRead
	}

	for totalRead < expectedLength && totalRead < config.Modbus.MaxADULength {
		if !deadline.IsZero() && time.Now().After(deadline) {
			return nil, fmt.Errorf("读取超时")
		}
		n, err := r.Read(buffer[totalRead:])
		if err != nil {
			return nil, err
		}
		totalRead += n
	}

	return buffer[:totalRead], nil
}

// readSlaveResponse 从Slave读取响应
func readSlaveResponse(r io.Reader, deadline time.Time, expectedSlaveID, expectedFunctionCode byte) ([]byte, error) {
	buffer := make([]byte, config.Modbus.MaxADULength)
	totalRead := 0

	for totalRead < 3 {
		if !deadline.IsZero() && time.Now().After(deadline) {
			return nil, fmt.Errorf("读取响应超时")
		}
		n, err := r.Read(buffer[totalRead:])
		if err != nil {
			return nil, err
		}
		totalRead += n
	}

	if totalRead < 3 {
		return nil, fmt.Errorf("响应数据包太短")
	}

	slaveID := buffer[0]
	functionCode := buffer[1]

	if slaveID != expectedSlaveID {
		return nil, fmt.Errorf("从站ID不匹配: 期望 %d, 收到 %d", expectedSlaveID, slaveID)
	}

	var expectedLength int
	if functionCode&0x80 != 0 {
		expectedLength = 5
	} else {
		switch functionCode {
		case 0x01, 0x02:
			if totalRead >= 3 {
				byteCount := int(buffer[2])
				expectedLength = 5 + byteCount
			} else {
				expectedLength = 5
			}
		case 0x03, 0x04:
			if totalRead >= 3 {
				byteCount := int(buffer[2])
				expectedLength = 5 + byteCount
			} else {
				expectedLength = 5
			}
		case 0x05, 0x06, 0x0F, 0x10:
			expectedLength = 8
		default:
			expectedLength = 8
		}
	}

	for totalRead < expectedLength && totalRead < config.Modbus.MaxADULength {
		if !deadline.IsZero() && time.Now().After(deadline) {
			return nil, fmt.Errorf("读取响应超时")
		}
		n, err := r.Read(buffer[totalRead:])
		if err != nil {
			return nil, err
		}
		totalRead += n
	}

	return buffer[:totalRead], nil
}

// worker 处理请求的工作协程
func worker(id int) {
	defer wg.Done()
	defer recoverFromPanic(fmt.Sprintf("工作协程%d", id))
	addLogWithLevel("INFO", fmt.Sprintf("工作协程 %d 启动", id))

	for {
		if !running {
			break
		}

		select {
		case req, ok := <-queue:
			if !ok || !running {
				break
			}

			atomic.AddInt64(&totalRequests, 1)
			startTime := time.Now()

			switch req.Type {
			case TCP_REQ:
				addLogWithLevel("DEBUG", fmt.Sprintf("[工作协程%d] 开始处理TCP请求", id))
				handleTCPRequest(req)
			case RTU_REQ:
				addLogWithLevel("DEBUG", fmt.Sprintf("[工作协程%d] 开始处理RTU请求", id))
				handleRTURequest(req)
			case MQTT_REQ:
				addLogWithLevel("DEBUG", fmt.Sprintf("[工作协程%d] 开始处理MQTT请求", id))
				handleMQTTRequestData(req)
			}

			processingTime := time.Since(startTime)
			addLogWithLevel("DEBUG", fmt.Sprintf("[工作协程%d] 请求处理完成，耗时: %v", id, processingTime))
		case <-time.After(100 * time.Millisecond):
			// 定期检查running状态，避免无限等待
			continue
		}
	}
	addLogWithLevel("INFO", fmt.Sprintf("工作协程 %d 退出", id))
}

// handleTCPRequest 处理TCP请求
func handleTCPRequest(req Request) {
	defer recoverFromPanic("TCP请求处理")
	//defer req.ClientSock.Close()

	if len(req.Data) < 8 {
		addLogWithLevel("ERROR", "[TCP模式] 请求数据包太短，长度不足8字节")
		return
	}

	slaveID := req.Data[6]
	functionCode := req.Data[7]
	addLogWithLevel("INFO", fmt.Sprintf("[TCP模式] 收到TCP请求，从站ID: %d, 功能码: %d, 数据长度: %d字节, 原始数据: %x",
		slaveID, functionCode, len(req.Data), req.Data))

	rtuRequest := req.Data[6:]
	crc := calculateCRC(rtuRequest[:len(rtuRequest)])
	rtuRequest = append(rtuRequest, byte(crc&0xFF), byte(crc>>8))
	addLogWithLevel("DEBUG", fmt.Sprintf("[TCP模式] 转换为RTU格式，添加CRC: %x", rtuRequest))

	slaveMutex.Lock()
	defer slaveMutex.Unlock()

	_, err := slaveFd.Write(rtuRequest)
	if err != nil {
		addLogWithLevel("ERROR", fmt.Sprintf("[TCP模式] 写入从站失败: %v", err))
		atomic.AddInt64(&errorRequests, 1)
		return
	}
	addLogWithLevel("DEBUG", fmt.Sprintf("[TCP模式] RTU请求已发送到从站串口，数据: %x", rtuRequest))

	deadline := time.Now().Add(time.Duration(config.Serial.TimeoutMs) * time.Millisecond)
	response, err := readSlaveResponse(slaveFd, deadline, slaveID, functionCode)
	if err != nil {
		addLogWithLevel("ERROR", fmt.Sprintf("[TCP模式] 读取从站响应失败: %v", err))
		atomic.AddInt64(&errorRequests, 1)
		return
	}
	addLogWithLevel("DEBUG", fmt.Sprintf("[TCP模式] 从站响应原始数据: %x", response))

	if len(response) < 2 {
		addLogWithLevel("ERROR", "[TCP模式] 从站响应太短，长度不足2字节")
		atomic.AddInt64(&errorRequests, 1)
		return
	}

	tcpResponse := make([]byte, 6+len(response)-2)
	copy(tcpResponse[0:6], req.Data[0:6])
	tcpResponse[4] = byte((len(response) - 2) >> 8)
	tcpResponse[5] = byte((len(response) - 2) & 0xFF)
	copy(tcpResponse[6:], response[:len(response)-2])
	addLogWithLevel("DEBUG", fmt.Sprintf("[TCP模式] 转换为TCP格式，移除CRC: %x", tcpResponse))

	_, err = req.ClientSock.Write(tcpResponse)
	if err != nil {
		addLogWithLevel("ERROR", fmt.Sprintf("[TCP模式] 发送TCP响应失败: %v", err))
		atomic.AddInt64(&errorRequests, 1)
	} else {
		addLogWithLevel("INFO", fmt.Sprintf("[TCP模式] TCP响应发送成功，数据长度: %d字节, 响应数据: %x",
			len(tcpResponse), tcpResponse))
		atomic.AddInt64(&successRequests, 1)
	}
}

// handleRTURequest 处理RTU请求
func handleRTURequest(req Request) {
	defer recoverFromPanic("RTU请求处理")
	if len(req.Data) < 4 {
		addLogWithLevel("ERROR", "[RTU模式] 请求数据包太短，长度不足4字节")
		return
	}

	slaveID := req.Data[0]
	functionCode := req.Data[1]
	addLogWithLevel("INFO", fmt.Sprintf("[RTU模式] 收到主站RTU请求，从站ID: %d, 功能码: %d, 数据长度: %d字节, 原始数据: %x",
		slaveID, functionCode, len(req.Data), req.Data))

	slaveMutex.Lock()
	defer slaveMutex.Unlock()

	_, err := slaveFd.Write(req.Data)
	if err != nil {
		addLogWithLevel("ERROR", fmt.Sprintf("[RTU模式] 写入从站失败: %v", err))
		atomic.AddInt64(&errorRequests, 1)
		return
	}
	addLogWithLevel("DEBUG", fmt.Sprintf("[RTU模式] RTU请求已转发到从站串口，数据: %x", req.Data))

	deadline := time.Now().Add(time.Duration(config.Serial.TimeoutMs) * time.Millisecond)
	response, err := readSlaveResponse(slaveFd, deadline, slaveID, functionCode)
	if err != nil {
		addLogWithLevel("ERROR", fmt.Sprintf("[RTU模式] 读取从站响应失败: %v", err))
		atomic.AddInt64(&errorRequests, 1)
		return
	}
	addLogWithLevel("DEBUG", fmt.Sprintf("[RTU模式] 从站响应原始数据: %x", response))

	_, err = masterFd.Write(response)
	if err != nil {
		addLogWithLevel("ERROR", fmt.Sprintf("[RTU模式] 发送RTU响应到主站失败: %v", err))
		atomic.AddInt64(&errorRequests, 1)
	} else {
		addLogWithLevel("INFO", fmt.Sprintf("[RTU模式] RTU响应发送成功，数据长度: %d字节, 响应数据: %x",
			len(response), response))
		atomic.AddInt64(&successRequests, 1)
	}
}

// handleMQTTRequestData 处理MQTT请求数据
func handleMQTTRequestData(req Request) {
	defer recoverFromPanic("MQTT数据处理")
	if len(req.Data) < 8 {
		addLogWithLevel("ERROR", "[MQTT模式] 请求数据包太短，长度不足8字节")
		return
	}

	slaveID := req.Data[6]
	functionCode := req.Data[7]
	addLogWithLevel("INFO", fmt.Sprintf("[MQTT模式] 收到MQTT请求，从站ID: %d, 功能码: %d, 数据长度: %d字节, 原始数据: %x",
		slaveID, functionCode, len(req.Data), req.Data))

	rtuRequest := req.Data[6:]
	crc := calculateCRC(rtuRequest[:len(rtuRequest)])
	rtuRequest = append(rtuRequest, byte(crc&0xFF), byte(crc>>8))
	addLogWithLevel("DEBUG", fmt.Sprintf("[MQTT模式] 转换为RTU格式，添加CRC: %x", rtuRequest))

	slaveMutex.Lock()
	defer slaveMutex.Unlock()

	_, err := slaveFd.Write(rtuRequest)
	if err != nil {
		addLogWithLevel("ERROR", fmt.Sprintf("[MQTT模式] 写入从站失败: %v", err))
		atomic.AddInt64(&errorRequests, 1)
		return
	}
	addLogWithLevel("DEBUG", fmt.Sprintf("[MQTT模式] RTU请求已发送到从站串口，数据: %x", rtuRequest))

	deadline := time.Now().Add(time.Duration(config.Serial.TimeoutMs) * time.Millisecond)
	response, err := readSlaveResponse(slaveFd, deadline, slaveID, functionCode)
	if err != nil {
		addLogWithLevel("ERROR", fmt.Sprintf("[MQTT模式] 读取从站响应失败: %v", err))
		atomic.AddInt64(&errorRequests, 1)
		return
	}
	addLogWithLevel("DEBUG", fmt.Sprintf("[MQTT模式] 从站响应原始数据: %x", response))

	if len(response) < 2 {
		addLogWithLevel("ERROR", "[MQTT模式] 从站响应太短，长度不足2字节")
		atomic.AddInt64(&errorRequests, 1)
		return
	}

	tcpResponse := make([]byte, 6+len(response)-2)
	copy(tcpResponse[0:6], req.Data[0:6])
	tcpResponse[4] = byte((len(response) - 2) >> 8)
	tcpResponse[5] = byte((len(response) - 2) & 0xFF)
	copy(tcpResponse[6:], response[:len(response)-2])
	addLogWithLevel("DEBUG", fmt.Sprintf("[MQTT模式] 转换为TCP格式，移除CRC: %x", tcpResponse))

	// 发布响应到MQTT
	topic := fmt.Sprintf("device/%s/response", config.Network.DeviceID)
	if token := mqttClient.Publish(topic, 1, false, tcpResponse); token.Wait() && token.Error() != nil {
		addLogWithLevel("ERROR", fmt.Sprintf("[MQTT模式] 发布MQTT响应失败: %v", token.Error()))
		atomic.AddInt64(&errorRequests, 1)
	} else {
		addLogWithLevel("INFO", fmt.Sprintf("[MQTT模式] MQTT响应发布成功，主题: %s, 数据长度: %d字节, 响应数据: %x",
			topic, len(tcpResponse), tcpResponse))
		atomic.AddInt64(&successRequests, 1)
	}
}

// rtuListener RTU监听器
func rtuListener() {
	defer wg.Done()
	defer recoverFromPanic("RTU监听器")
	startMsg := "RTU监听器启动"
	addLogWithLevel("INFO", startMsg)

	for running {
		deadline := time.Now().Add(time.Duration(config.Serial.TimeoutMs) * time.Millisecond)
		data, err := readMasterRequest(masterFd, deadline)
		if err != nil {
			if running {
				errMsg := fmt.Sprintf("读取主站请求失败: %v", err)
				log.Printf(errMsg)
				addLogWithLevel("DEBUG", errMsg)
			}
			continue
		}

		addLogWithLevel("DEBUG", fmt.Sprintf("RTU监听器收到主站请求，数据长度: %d字节, 数据: %x", len(data), data))

		req := Request{
			Type: RTU_REQ,
			Data: data,
		}

		if running {
			select {
			case queue <- req:
				addLogWithLevel("DEBUG", "RTU请求已加入处理队列")
			default:
				warnMsg := "请求队列已满，丢弃RTU请求"
				log.Println(warnMsg)
				addLogWithLevel("WARN", warnMsg)
			}
		}
	}
	exitMsg := "RTU监听器退出"
	addLogWithLevel("INFO", exitMsg)
}

// tcpListener TCP监听器
func tcpListener() {
	defer wg.Done()
	defer recoverFromPanic("TCP监听器")
	logMsg := fmt.Sprintf("TCP监听器在端口 %d 启动", config.Network.TCPPort)
	addLogWithLevel("INFO", logMsg)

	for running {
		conn, err := serverSock.Accept()
		if err != nil {
			if running {
				errMsg := fmt.Sprintf("TCP接受连接错误: %v", err)
				addLogWithLevel("ERROR", errMsg)
			}
			return
		}

		go func(c net.Conn) {
			defer c.Close()
			defer recoverFromPanic(fmt.Sprintf("TCP连接处理-%s", c.RemoteAddr()))
			atomic.AddInt32(&tcpConnCount, 1)
			defer atomic.AddInt32(&tcpConnCount, -1)

			connMsg := fmt.Sprintf("新TCP连接: %s", c.RemoteAddr())
			addLogWithLevel("INFO", connMsg)

			buffer := make([]byte, config.Modbus.MaxADULength)

			for running {
				// 设置读取超时，定期检查running状态
				c.SetReadDeadline(time.Now().Add(5 * time.Second))
				n, err := c.Read(buffer)
				if err != nil {
					if !running {
						addLogWithLevel("DEBUG", fmt.Sprintf("TCP连接因服务关闭而断开: %s", c.RemoteAddr()))
					} else {
						addLogWithLevel("DEBUG", fmt.Sprintf("TCP连接断开: %s", c.RemoteAddr()))
					}
					return
				}

				atomic.AddInt64(&totalRequests, 1)
				addLogWithLevel("DEBUG", fmt.Sprintf("收到TCP请求，长度: %d，来源: %s", n, c.RemoteAddr()))

				req := Request{
					Type:       TCP_REQ,
					ClientSock: c,
					Data:       make([]byte, n),
				}
				copy(req.Data, buffer[:n])

				if running {
					select {
					case queue <- req:
					default:
						warnMsg := "请求队列已满，丢弃TCP请求"
						addLogWithLevel("WARN", warnMsg)
						return
					}
				} else {
					return
				}
			}
		}(conn)
	}

	exitMsg := "TCP监听器退出"
	addLogWithLevel("INFO", exitMsg)
}

// mqttListener MQTT监听器
func mqttListener() {
	defer wg.Done()
	defer recoverFromPanic("MQTT监听器")
	startMsg := "MQTT监听器启动"
	addLogWithLevel("INFO", startMsg)

	opts := mqtt.NewClientOptions().
		AddBroker(config.Network.MqttBroker).
		SetClientID(config.Network.DeviceID).
		SetUsername(config.Network.MqttUsername).
		SetPassword(config.Network.MqttPassword).
		SetAutoReconnect(true).
		SetConnectRetry(true).
		SetConnectRetryInterval(5 * time.Second)

	mqttClient = mqtt.NewClient(opts)
	if token := mqttClient.Connect(); token.Wait() && token.Error() != nil {
		errMsg := fmt.Sprintf("连接MQTT代理失败: %v", token.Error())
		addLogWithLevel("ERROR", errMsg)
		return
	}

	topic := fmt.Sprintf("device/%s/request", config.Network.DeviceID)
	if token := mqttClient.Subscribe(topic, 1, handleMqttRequest); token.Wait() && token.Error() != nil {
		errMsg := fmt.Sprintf("订阅MQTT主题失败: %v", token.Error())
		addLogWithLevel("ERROR", errMsg)
		return
	}

	successMsg := fmt.Sprintf("MQTT客户端连接成功，订阅主题: %s", topic)
	addLogWithLevel("INFO", successMsg)

	for running {
		time.Sleep(time.Second)
	}

	exitMsg := "MQTT监听器退出"
	addLogWithLevel("INFO", exitMsg)
}

// handleMqttRequest 处理MQTT请求
func handleMqttRequest(client mqtt.Client, msg mqtt.Message) {
	defer recoverFromPanic("MQTT请求处理")
	payload := msg.Payload()
	addLogWithLevel("DEBUG", fmt.Sprintf("MQTT监听器收到请求，主题: %s, 数据长度: %d字节, 数据: %x",
		msg.Topic(), len(payload), payload))

	req := Request{
		Type: MQTT_REQ,
		Data: payload,
	}

	if running {
		select {
		case queue <- req:
			addLogWithLevel("DEBUG", "MQTT请求已加入处理队列")
		default:
			warnMsg := "请求队列已满，丢弃MQTT请求"
			addLogWithLevel("WARN", warnMsg)
		}
	}
}

// initTCPServer 初始化TCP服务器
func initTCPServer(port int) (net.Listener, error) {
	listener, err := net.Listen("tcp", fmt.Sprintf(":%d", port))
	if err != nil {
		return nil, err
	}
	return listener, nil
}

// saveConfig 保存配置到文件
func saveConfig(filename string, cfg Config) error {
	data, err := yaml.Marshal(cfg)
	if err != nil {
		return err
	}
	return os.WriteFile(filename, data, 0644)
}

// updateSystemStatus 更新系统状态
func updateSystemStatus() {
	statusMutex.Lock()
	defer statusMutex.Unlock()

	systemStatus.StartTime = startTime
	systemStatus.Uptime = time.Since(startTime).String()
	systemStatus.TCPConnections = atomic.LoadInt32(&tcpConnCount)
	systemStatus.TotalRequests = atomic.LoadInt64(&totalRequests)
	systemStatus.SuccessRequests = atomic.LoadInt64(&successRequests)
	systemStatus.ErrorRequests = atomic.LoadInt64(&errorRequests)
	systemStatus.QueueLength = len(queue)
	systemStatus.LastActivity = time.Now()

	// 检查MQTT连接状态
	if mqttClient != nil {
		systemStatus.MQTTConnected = mqttClient.IsConnected()
	}

	// 检查串口状态
	systemStatus.SerialMasterOK = (masterFd != nil)
	systemStatus.SerialSlaveOK = (slaveFd != nil)
}

// getSystemStatus 获取系统状态
func getSystemStatus() SystemStatus {
	statusMutex.RLock()
	defer statusMutex.RUnlock()
	return systemStatus
}

// startStatusMonitor 启动状态监控
func startStatusMonitor() {
	go func() {
		defer recoverFromPanic("状态监控器")
		ticker := time.NewTicker(1 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				if !running {
					return
				}
				updateSystemStatus()
			}
		}
	}()
}

// addLog 添加日志到缓冲区
func addLog(message string) {
	addLogWithLevel("INFO", message)
}

// initLogSystem 初始化日志系统
func initLogSystem() error {
	// 创建日志目录
	if err := os.MkdirAll(logDir, 0755); err != nil {
		return fmt.Errorf("创建日志目录失败: %v", err)
	}

	// 打开当前日志文件
	logFileName := filepath.Join(logDir, "gateway.log")
	var err error
	logFile, err = os.OpenFile(logFileName, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return fmt.Errorf("打开日志文件失败: %v", err)
	}

	// 获取当前文件大小
	if stat, err := logFile.Stat(); err == nil {
		currentLogSize = stat.Size()
	}

	// 写入启动分隔线和初始日志
	separator := fmt.Sprintf("[%s] [INFO] ====================================\n", time.Now().Format("2006-01-02 15:04:05"))
	initialLog := fmt.Sprintf("[%s] [INFO] Modbus网关启动 - 日志系统初始化完成\n", time.Now().Format("2006-01-02 15:04:05"))
	versionLog := fmt.Sprintf("[%s] [INFO] 系统版本: Modbus网关 v1.0.0\n", time.Now().Format("2006-01-02 15:04:05"))
	configLog := fmt.Sprintf("[%s] [INFO] 日志级别: %s, 日志目录: %s\n", time.Now().Format("2006-01-02 15:04:05"), logLevel, logDir)

	logFile.WriteString(separator)
	logFile.WriteString(initialLog)
	logFile.WriteString(versionLog)
	logFile.WriteString(configLog)
	logFile.Sync()

	return nil
}

// closeLogSystem 关闭日志系统
func closeLogSystem() {
	if logFile != nil {
		logFile.Close()
	}
}

// rotateLogFile 轮转日志文件
func rotateLogFile() error {
	if logFile != nil {
		logFile.Close()
	}

	// 重命名当前日志文件
	timestamp := time.Now().Format("20060102_150405")
	oldName := filepath.Join(logDir, "gateway.log")
	newName := filepath.Join(logDir, fmt.Sprintf("gateway_%s.log", timestamp))

	if err := os.Rename(oldName, newName); err != nil {
		return fmt.Errorf("重命名日志文件失败: %v", err)
	}

	// 创建新的日志文件
	var err error
	logFile, err = os.OpenFile(oldName, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return fmt.Errorf("创建新日志文件失败: %v", err)
	}

	currentLogSize = 0

	// 清理旧日志文件
	cleanupOldLogs()

	return nil
}

// cleanupOldLogs 清理旧日志文件
func cleanupOldLogs() {
	files, err := filepath.Glob(filepath.Join(logDir, "gateway_*.log"))
	if err != nil {
		return
	}

	if len(files) <= maxLogFiles {
		return
	}

	// 按修改时间排序
	sort.Slice(files, func(i, j int) bool {
		stat1, _ := os.Stat(files[i])
		stat2, _ := os.Stat(files[j])
		return stat1.ModTime().Before(stat2.ModTime())
	})

	// 删除最旧的文件
	for i := 0; i < len(files)-maxLogFiles; i++ {
		os.Remove(files[i])
	}
}

// addLogWithLevel 添加带等级的日志到缓冲区和文件
func addLogWithLevel(level, message string) {
	// 检查日志等级
	if !shouldLog(level) {
		return
	}

	logMutex.Lock()
	defer logMutex.Unlock()

	timestamp := time.Now().Format("2006-01-02 15:04:05")
	logEntry := fmt.Sprintf("[%s] [%s] %s", timestamp, level, message)

	// 写入文件
	if logFile != nil {
		logLine := logEntry + "\n"
		n, err := logFile.WriteString(logLine)
		if err == nil {
			currentLogSize += int64(n)
			logFile.Sync() // 强制刷新到磁盘

			// 检查是否需要轮转
			if currentLogSize >= int64(maxLogSize) {
				go rotateLogFile() // 异步轮转
			}
		}
	}

	// 保留少量日志在内存中用于web显示
	logBuffer = append(logBuffer, logEntry)
	if len(logBuffer) > maxLogLines {
		logBuffer = logBuffer[len(logBuffer)-maxLogLines:]
	}
}

// shouldLog 检查是否应该记录该等级的日志
func shouldLog(level string) bool {
	levels := map[string]int{
		"DEBUG": 0,
		"INFO":  1,
		"WARN":  2,
		"ERROR": 3,
	}

	currentLevel, exists := levels[logLevel]
	if !exists {
		currentLevel = 1 // 默认INFO
	}

	targetLevel, exists := levels[level]
	if !exists {
		targetLevel = 1 // 默认INFO
	}

	return targetLevel >= currentLevel
}

// getLogs 获取日志
func getLogs() []string {
	logMutex.RLock()
	defer logMutex.RUnlock()

	// 返回日志副本
	logs := make([]string, len(logBuffer))
	copy(logs, logBuffer)
	return logs
}

// Web界面相关代码

const webTemplate = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modbus网关管理中心 - 企业级工业网关</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #667eea 100%);
            min-height: 100vh;
            color: #2c3e50;
            overflow-x: hidden;
        }
        
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
            z-index: -1;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
            position: relative;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
            border-radius: 2px;
        }
        
        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 15px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
            letter-spacing: -0.02em;
            background: linear-gradient(135deg, #ffffff 0%, #e3f2fd 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.95;
            font-weight: 400;
            text-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
        
        .header .subtitle {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin-top: 10px;
        }
        
        .header .badge {
            background: rgba(255,255,255,0.2);
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: 1fr 2fr 1fr;
            gap: 25px;
            margin-bottom: 40px;
        }
        
        @media (max-width: 1200px) {
            .dashboard {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
        
        .panel {
            background: rgba(255,255,255,0.98);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.15), 0 8px 25px rgba(0,0,0,0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255,255,255,0.2);
            position: relative;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .panel:hover {
            transform: translateY(-2px);
            box-shadow: 0 25px 70px rgba(0,0,0,0.2), 0 10px 30px rgba(0,0,0,0.15);
        }
        
        .panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
            background-size: 200% 100%;
            animation: shimmer 3s ease-in-out infinite;
        }
        
        @keyframes shimmer {
            0%, 100% { background-position: 200% 0; }
            50% { background-position: -200% 0; }
        }
        
        .status-panel {
            grid-column: 1;
        }
        
        .config-panel {
            grid-column: 2;
        }
        
        .logs-panel {
            grid-column: 3;
        }
        

        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .status-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border-left: 4px solid #007bff;
            transition: transform 0.3s ease;
        }
        
        .status-card:hover {
            transform: translateY(-2px);
        }
        
        .status-card.success {
            border-left-color: #28a745;
        }
        
        .status-card.warning {
            border-left-color: #ffc107;
        }
        
        .status-card.danger {
            border-left-color: #dc3545;
        }
        
        .status-value {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .status-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .config-panel {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        .config-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .tab-button {
            background: none;
            border: none;
            padding: 12px 20px;
            cursor: pointer;
            font-size: 1rem;
            color: #6c757d;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .tab-button.active {
            color: #007bff;
            border-bottom-color: #007bff;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .form-section {
            margin-bottom: 25px;
        }
        
        .form-section h4 {
            color: #495057;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            font-weight: 600;
            margin-bottom: 5px;
            color: #495057;
        }
        
        .form-group input,
        .form-group select {
            padding: 10px 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }
        
        .btn {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 10px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }
        
        .btn-danger:hover {
            box-shadow: 0 4px 12px rgba(220,53,69,0.3);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        }
        
        .btn-success:hover {
            box-shadow: 0 4px 12px rgba(40,167,69,0.3);
        }
        
        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
            align-items: center;
            gap: 10px;
        }
        
        .alert.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .button-group {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
        }
        
        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .config-tabs {
                flex-wrap: wrap;
            }
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 重启等待动画 */
        .restart-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            flex-direction: column;
        }
        
        .restart-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        .restart-message {
            color: white;
            font-size: 18px;
            margin-top: 20px;
            text-align: center;
        }
        
        .restart-progress {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
        
        /* 日志面板样式 */
        .logs-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .log-level-control {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .log-level-control label {
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }
        
        .log-level-control select {
            padding: 6px 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            cursor: pointer;
            transition: border-color 0.3s ease;
        }
        
        .log-level-control select:focus {
            outline: none;
            border-color: #007bff;
        }
        
        .log-files-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            height: 400px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
        }
        
        .log-files-container::-webkit-scrollbar {
            width: 8px;
        }
        
        .log-files-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }
        
        .log-files-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }
        
        .log-files-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        
        .log-file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            cursor: pointer;
            user-select: none;
        }
        
        .log-file-item:hover {
            border-color: #007bff;
            box-shadow: 0 4px 8px rgba(0,123,255,0.15);
            transform: translateY(-1px);
            background: #f8f9ff;
        }
        
        .log-file-item:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0,123,255,0.1);
        }
        
        .log-file-info {
            flex: 1;
        }
        
        .log-file-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 6px;
            font-size: 14px;
        }
        
        .log-file-name i {
            color: #007bff;
            margin-right: 8px;
        }
        
        .log-file-details {
            font-size: 12px;
            color: #666;
        }
        
        .log-file-size {
            margin-right: 15px;
            font-weight: 500;
        }
        
        .log-file-date {
            color: #888;
        }
        
        .log-file-download-icon {
            color: #007bff;
            font-size: 18px;
            opacity: 0.7;
            transition: all 0.2s ease;
        }
        
        .log-file-item:hover .log-file-download-icon {
            opacity: 1;
            transform: scale(1.1);
        }
        
        .btn-sm {
            padding: 8px 16px;
            font-size: 12px;
            border-radius: 6px;
        }
        
        /* 模态框样式 */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .modal-content {
            background-color: white;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .modal-header h3 {
            margin: 0;
            color: #333;
        }
        
        .close {
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            color: #999;
            transition: color 0.2s;
        }
        
        .close:hover {
            color: #333;
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .config-group {
            margin-bottom: 15px;
        }
        
        .config-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        
        .config-group input[type="number"] {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .config-group input[type="checkbox"] {
            margin-right: 8px;
        }
        
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            padding: 20px;
            border-top: 1px solid #e9ecef;
        }
        
        /* 进度条通知样式 */
        .notification-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 2000;
            max-width: 400px;
        }
        
        .notification {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            margin-bottom: 10px;
            overflow: hidden;
            transform: translateX(100%);
            transition: all 0.3s ease;
            border-left: 4px solid #007bff;
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification.success {
            border-left-color: #28a745;
        }
        
        .notification.error {
            border-left-color: #dc3545;
        }
        
        .notification.warning {
            border-left-color: #ffc107;
        }
        
        .notification-content {
            padding: 15px 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .notification-icon {
            font-size: 18px;
            flex-shrink: 0;
        }
        
        .notification.success .notification-icon {
            color: #28a745;
        }
        
        .notification.error .notification-icon {
            color: #dc3545;
        }
        
        .notification.warning .notification-icon {
            color: #ffc107;
        }
        
        .notification-message {
            flex: 1;
            font-size: 14px;
            color: #333;
        }
        
        .notification-progress {
            height: 3px;
            background: #f8f9fa;
            position: relative;
            overflow: hidden;
        }
        
        .notification-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #0056b3);
            width: 0%;
            transition: width 0.1s ease;
        }
        
        .notification.success .notification-progress-bar {
            background: linear-gradient(90deg, #28a745, #1e7e34);
        }
        
        .notification.error .notification-progress-bar {
            background: linear-gradient(90deg, #dc3545, #c82333);
        }
        
        .notification.warning .notification-progress-bar {
            background: linear-gradient(90deg, #ffc107, #e0a800);
        }
        
        /* 卡片头部控制区域样式 */
        .card-header-with-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 0 0 16px 0;
            border-bottom: 1px solid #e9ecef;
            height: 56px;
            box-sizing: border-box;
        }
        
        .card-header-with-controls h2 {
            margin: 0;
            color: #333;
            font-size: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
            flex: 1;
        }
        
        .card-header-with-controls h2 i {
            color: #007bff;
        }
        
        .logs-controls,
        .status-controls,
        .config-controls {
            display: flex;
            gap: 8px;
            align-items: center;
            flex-shrink: 0;
        }
        
        .icon-button.config:hover {
            transform: rotate(90deg);
        }
        
        /* 通用图标按钮样式 */
        .icon-button {
            background: transparent;
            color: #6c757d;
            border: none;
            padding: 10px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: all 0.2s ease;
            width: 40px;
            height: 40px;
            box-sizing: border-box;
        }
        
        .icon-button:hover {
            background: #f8f9fa;
            color: #007bff;
        }
        
        .icon-button i {
            opacity: 0.7;
        }
        
        .icon-button:hover i {
            opacity: 1;
        }
        
        .icon-button.refresh:hover {
            transform: rotate(180deg);
        }
        
        .config-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 2000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .config-modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }
        
        .config-modal-content {
            position: relative;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow: hidden;
            animation: modalSlideIn 0.3s ease-out;
        }
        
        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }
        
        .config-modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .config-modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .config-modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: background-color 0.2s ease;
        }
        
        .config-modal-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .config-modal-body {
            padding: 24px;
            max-height: 60vh;
            overflow-y: auto;
        }
        
        .config-modal-footer {
            padding: 20px 24px;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }
        
        .config-item {
            margin-bottom: 15px;
        }
        
        .config-item:last-child {
            margin-bottom: 0;
        }
        
        .config-item label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
            font-size: 14px;
        }
        
        .config-item select,
        .config-item input[type="number"] {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.2s ease;
        }
        
        .config-item select:focus,
        .config-item input[type="number"]:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }
        
        .config-item input[type="checkbox"] {
            margin-right: 8px;
        }
        
        .config-item label:has(input[type="checkbox"]) {
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        
        .config-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #e9ecef;
        }
        
        .btn-apply {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
            flex: 1;
            justify-content: center;
        }
        
        .btn-apply:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
        }
        
        .btn-reset {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
            flex: 1;
            justify-content: center;
        }
        
        .btn-reset:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.4);
        }
        
        .btn-cancel {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .btn-cancel:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
        }
        
        .log-files-loading {
            text-align: center;
            color: #888;
            padding: 50px 0;
        }
        
        .logs-empty {
            text-align: center;
            color: #888;
            padding: 50px 0;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-microchip"></i> Modbus网关管理中心</h1>
            <p>F+4000 嵌入式网关设备 - 实时监控与配置管理</p>
            <div class="subtitle">
                <span class="badge"><i class="fas fa-shield-alt"></i> 企业级</span>
                <span class="badge"><i class="fas fa-bolt"></i> 高性能</span>
                <span class="badge"><i class="fas fa-cogs"></i> 工业级</span>
            </div>
        </div>
        
        <div class="dashboard">
            <div class="status-panel panel">
                <div class="card-header-with-controls">
                    <h2><i class="fas fa-tachometer-alt"></i> 系统状态</h2>
                    <div class="status-controls">
                        <button class="icon-button refresh" onclick="updateStatus()" title="刷新状态">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="status-grid" id="statusGrid">
                    <!-- 状态卡片将通过JavaScript动态生成 -->
                </div>
            </div>
            
            <div class="config-panel panel">
                <div class="card-header-with-controls">
                    <h2><i class="fas fa-cogs"></i> 配置管理</h2>
                    <div class="config-controls">
                        <!-- 占位符，保持高度一致 -->
                    </div>
                </div>
                
                <div class="config-tabs">
                    <button class="tab-button active" onclick="switchTab('serial')">
                        <i class="fas fa-plug"></i> 串口配置
                    </button>
                    <button class="tab-button" onclick="switchTab('network')">
                        <i class="fas fa-wifi"></i> 网络配置
                    </button>
                    <button class="tab-button" onclick="switchTab('modbus')">
                        <i class="fas fa-exchange-alt"></i> Modbus配置
                    </button>
                </div>
                
                <div id="alert" class="alert"></div>
        
                <form id="configForm">
                    <div id="serial-tab" class="tab-content active">
                        <div class="form-section">
                            <h4>串口设备</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="master_device">主站设备</label>
                                    <input type="text" id="master_device" name="master_device" value="{{.Serial.MasterDevice}}">
                                </div>
                                <div class="form-group">
                                    <label for="slave_device">从站设备</label>
                                    <input type="text" id="slave_device" name="slave_device" value="{{.Serial.SlaveDevice}}">
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-section">
                            <h4>通信参数</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="baud_rate">波特率</label>
                                    <select id="baud_rate" name="baud_rate">
                                        <option value="9600" {{if eq .Serial.BaudRate 9600}}selected{{end}}>9600</option>
                                        <option value="19200" {{if eq .Serial.BaudRate 19200}}selected{{end}}>19200</option>
                                        <option value="38400" {{if eq .Serial.BaudRate 38400}}selected{{end}}>38400</option>
                                        <option value="57600" {{if eq .Serial.BaudRate 57600}}selected{{end}}>57600</option>
                                        <option value="115200" {{if eq .Serial.BaudRate 115200}}selected{{end}}>115200</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="data_bits">数据位</label>
                                    <select id="data_bits" name="data_bits">
                                        <option value="7" {{if eq .Serial.DataBits 7}}selected{{end}}>7</option>
                                        <option value="8" {{if eq .Serial.DataBits 8}}selected{{end}}>8</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="parity">校验位</label>
                                    <select id="parity" name="parity">
                                        <option value="N" {{if eq .Serial.Parity "N"}}selected{{end}}>无校验(N)</option>
                                        <option value="E" {{if eq .Serial.Parity "E"}}selected{{end}}>偶校验(E)</option>
                                        <option value="O" {{if eq .Serial.Parity "O"}}selected{{end}}>奇校验(O)</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="stop_bits">停止位</label>
                                    <select id="stop_bits" name="stop_bits">
                                        <option value="1" {{if eq .Serial.StopBits 1}}selected{{end}}>1</option>
                                        <option value="2" {{if eq .Serial.StopBits 2}}selected{{end}}>2</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="timeout_ms">超时时间(ms)</label>
                                    <input type="number" id="timeout_ms" name="timeout_ms" value="{{.Serial.TimeoutMs}}" min="100" max="10000">
                                </div>
                                <div class="form-group">
                                    <label for="mode">通信模式</label>
                                    <select id="mode" name="mode">
                                        <option value="RTU" {{if eq .Serial.Mode "RTU"}}selected{{end}}>RTU</option>
                                        <option value="ASCII" {{if eq .Serial.Mode "ASCII"}}selected{{end}}>ASCII</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="network-tab" class="tab-content">
                        <div class="form-section">
                            <h4>网络端口</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="tcp_port">TCP端口</label>
                                    <input type="number" id="tcp_port" name="tcp_port" value="{{.Network.TCPPort}}" min="1" max="65535">
                                </div>
                                <div class="form-group">
                                    <label for="web_port">Web管理端口</label>
                                    <input type="number" id="web_port" name="web_port" value="{{.Network.WebPort}}" min="1" max="65535">
                                </div>
                                <div class="form-group">
                                    <label for="max_clients">最大客户端数</label>
                                    <input type="number" id="max_clients" name="max_clients" value="{{.Network.MaxClients}}" min="1" max="100">
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-section">
                            <h4>MQTT配置</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="mqtt_broker">MQTT代理地址</label>
                                    <input type="text" id="mqtt_broker" name="mqtt_broker" value="{{.Network.MqttBroker}}">
                                </div>
                                <div class="form-group">
                                    <label for="device_id">设备ID</label>
                                    <input type="text" id="device_id" name="device_id" value="{{.Network.DeviceID}}">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="mqtt_username">MQTT用户名</label>
                                    <input type="text" id="mqtt_username" name="mqtt_username" value="{{.Network.MqttUsername}}">
                                </div>
                                <div class="form-group">
                                    <label for="mqtt_password">MQTT密码</label>
                                    <input type="password" id="mqtt_password" name="mqtt_password" value="{{.Network.MqttPassword}}">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="modbus-tab" class="tab-content">
                        <div class="form-section">
                            <h4>Modbus参数</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="max_adu_length">最大ADU长度</label>
                                    <input type="number" id="max_adu_length" name="max_adu_length" value="{{.Modbus.MaxADULength}}" min="64" max="1024">
                                </div>
                                <div class="form-group">
                                    <label for="queue_size">请求队列大小</label>
                                    <input type="number" id="queue_size" name="queue_size" value="{{.Runtime.QueueSize}}" min="10" max="10000">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="button-group">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> 保存配置
                        </button>
                        <button type="button" class="btn btn-danger" onclick="restartService()">
                            <i class="fas fa-redo"></i> 重启服务
                        </button>
                    </div>
                </form>
            </div>
            
            <div class="logs-panel panel">
                <div class="card-header-with-controls">
                    <h2><i class="fas fa-file-alt"></i> 日志管理</h2>
                    <div class="logs-controls">
                        <button class="icon-button refresh" onclick="refreshLogFiles()" title="刷新日志文件">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button class="icon-button config" onclick="showLogConfigModal()" title="日志配置">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                </div>

                <div class="log-files-container" id="logFilesContainer">
                    <div class="log-files-loading">
                        <i class="fas fa-spinner fa-spin"></i> 加载日志文件中...
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 重启等待覆盖层 -->
    <div id="restartOverlay" class="restart-overlay">
        <div class="restart-spinner"></div>
        <div class="restart-message">正在重启服务...</div>
        <div class="restart-progress" id="restartProgress">请稍候，服务重启中</div>
    </div>

    <script>
        let statusUpdateInterval;
        
        // 启动状态更新
        function startStatusUpdate() {
            if (statusUpdateInterval) {
                clearInterval(statusUpdateInterval);
            }
            updateStatus();
            statusUpdateInterval = setInterval(updateStatus, 2000); // 每2秒更新一次
        }
        
        // 页面加载完成后启动状态更新
        document.addEventListener('DOMContentLoaded', function() {
            startStatusUpdate();
            refreshLogFiles();
        });
        
        // 切换标签页
        function switchTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 移除所有按钮的active类
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示选中的标签内容
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // 激活对应的按钮
            event.target.classList.add('active');
        }
        
        // 更新系统状态
        function updateStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    updateStatusCards(data);
                })
                .catch(error => {
                    console.error('获取状态失败:', error);
                });
        }
        
        // 更新状态卡片
        function updateStatusCards(status) {
            const statusGrid = document.getElementById('statusGrid');
            
            const cards = [
                {
                    title: '运行时间',
                    value: formatUptime(status.uptime),
                    icon: 'fas fa-clock',
                    class: 'success'
                },
                {
                    title: 'TCP连接',
                    value: status.tcp_connections,
                    icon: 'fas fa-plug',
                    class: status.tcp_connections > 0 ? 'success' : 'warning'
                },
                {
                    title: 'MQTT状态',
                    value: status.mqtt_connected ? '已连接' : '未连接',
                    icon: 'fas fa-wifi',
                    class: status.mqtt_connected ? 'success' : 'danger'
                },
                {
                    title: '主站串口',
                    value: status.serial_master_ok ? '正常' : '异常',
                    icon: 'fas fa-microchip',
                    class: status.serial_master_ok ? 'success' : 'danger'
                },
                {
                    title: '从站串口',
                    value: status.serial_slave_ok ? '正常' : '异常',
                    icon: 'fas fa-memory',
                    class: status.serial_slave_ok ? 'success' : 'danger'
                },
                {
                    title: '总请求数',
                    value: status.total_requests,
                    icon: 'fas fa-exchange-alt',
                    class: 'success'
                },
                {
                    title: '成功请求',
                    value: status.success_requests,
                    icon: 'fas fa-check-circle',
                    class: 'success'
                },
                {
                    title: '错误请求',
                    value: status.error_requests,
                    icon: 'fas fa-exclamation-triangle',
                    class: status.error_requests > 0 ? 'warning' : 'success'
                },
                {
                    title: '队列长度',
                    value: status.queue_length,
                    icon: 'fas fa-list',
                    class: status.queue_length > 50 ? 'warning' : 'success'
                },
                {
                    title: '内存使用',
                    value: formatBytes(status.memory_usage),
                    icon: 'fas fa-memory',
                    class: 'success'
                }
            ];
            
            statusGrid.innerHTML = cards.map(card => 
                '<div class="status-card ' + card.class + '">' +
                    '<div class="status-value">' +
                        '<i class="' + card.icon + '"></i> ' + card.value +
                    '</div>' +
                    '<div class="status-label">' + card.title + '</div>' +
                '</div>'
            ).join('');
        }
        
        // 格式化运行时间
        function formatUptime(uptime) {
            const match = uptime.match(/(\d+h)?(\d+m)?(\d+\.?\d*s)?/);
            if (!match) return uptime;
            
            let result = '';
            if (match[1]) result += match[1] + ' ';
            if (match[2]) result += match[2] + ' ';
            if (match[3]) result += Math.floor(parseFloat(match[3])) + 's';
            
            return result.trim() || uptime;
        }
        
        // 格式化字节数
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // 表单提交处理
        document.getElementById('configForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const config = {
                serial: {
                    master_device: formData.get('master_device'),
                    slave_device: formData.get('slave_device'),
                    baud_rate: parseInt(formData.get('baud_rate')),
                    data_bits: parseInt(formData.get('data_bits')),
                    parity: formData.get('parity'),
                    stop_bits: parseInt(formData.get('stop_bits')),
                    timeout_ms: parseInt(formData.get('timeout_ms')),
                    mode: formData.get('mode')
                },
                network: {
                    tcp_port: parseInt(formData.get('tcp_port')),
                    web_port: parseInt(formData.get('web_port')),
                    max_clients: parseInt(formData.get('max_clients')),
                    mqtt_broker: formData.get('mqtt_broker'),
                    device_id: formData.get('device_id'),
                    mqtt_username: formData.get('mqtt_username'),
                    mqtt_password: formData.get('mqtt_password')
                },
                modbus: {
                    max_adu_length: parseInt(formData.get('max_adu_length'))
                },
                runtime: {
                    queue_size: parseInt(formData.get('queue_size'))
                }
            };

            fetch('/api/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                showAlert(data.message, data.success ? 'success' : 'error');
            })
            .catch(error => {
                showAlert('保存配置失败: ' + error.message, 'error');
            });
        });

        // 重启服务
        function restartService() {
            if (confirm('确定要重启服务吗？这将中断当前所有连接。')) {
                // 显示重启覆盖层
                const overlay = document.getElementById('restartOverlay');
                const progress = document.getElementById('restartProgress');
                overlay.style.display = 'flex';
                
                // 停止状态更新
                if (statusUpdateInterval) {
                    clearInterval(statusUpdateInterval);
                }
                
                let progressStep = 0;
                const progressMessages = [
                    '正在关闭当前服务...',
                    '正在释放网络端口...',
                    '正在启动新进程...',
                    '正在初始化服务...',
                    '服务重启完成，正在重新连接...'
                ];
                
                // 进度更新定时器
                const progressTimer = setInterval(() => {
                    if (progressStep < progressMessages.length - 1) {
                        progress.textContent = progressMessages[progressStep];
                        progressStep++;
                    }
                }, 2000);
                
                fetch('/api/restart', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        progress.textContent = progressMessages[progressMessages.length - 1];
                        // 等待服务重启完成后重新加载页面
                        setTimeout(() => {
                            clearInterval(progressTimer);
                            location.reload();
                        }, 8000);
                    } else {
                        clearInterval(progressTimer);
                        overlay.style.display = 'none';
                        showAlert('重启失败: ' + data.message, 'error');
                        // 重新启动状态更新
                        startStatusUpdate();
                    }
                })
                .catch(error => {
                    clearInterval(progressTimer);
                    overlay.style.display = 'none';
                    showAlert('重启服务失败: ' + error.message, 'error');
                    // 重新启动状态更新
                    startStatusUpdate();
                });
            }
        }

        // 显示进度条通知
        function showAlert(message, type = 'info', duration = 3000) {
            const container = document.getElementById('notificationContainer');
            const notification = document.createElement('div');
            const notificationId = 'notification-' + Date.now();
            
            notification.id = notificationId;
            notification.className = 'notification ' + type;
            
            const iconMap = {
                'success': 'fas fa-check-circle',
                'error': 'fas fa-exclamation-circle',
                'warning': 'fas fa-exclamation-triangle',
                'info': 'fas fa-info-circle'
            };
            
            notification.innerHTML = 
                '<div class="notification-content">' +
                    '<i class="notification-icon ' + (iconMap[type] || iconMap.info) + '"></i>' +
                    '<div class="notification-message">' + message + '</div>' +
                '</div>' +
                '<div class="notification-progress">' +
                    '<div class="notification-progress-bar"></div>' +
                '</div>';
            
            container.appendChild(notification);
            
            // 触发显示动画
            setTimeout(() => {
                notification.classList.add('show');
            }, 10);
            
            // 进度条动画
            const progressBar = notification.querySelector('.notification-progress-bar');
            let progress = 0;
            const interval = 50;
            const increment = (interval / duration) * 100;
            
            const progressInterval = setInterval(() => {
                progress += increment;
                progressBar.style.width = progress + '%';
                
                if (progress >= 100) {
                    clearInterval(progressInterval);
                    // 隐藏动画
                    notification.classList.remove('show');
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.remove();
                        }
                    }, 300);
                }
            }, interval);
            
            // 点击关闭
            notification.addEventListener('click', () => {
                clearInterval(progressInterval);
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.remove();
                    }
                }, 300);
            });
        }
        
        // 日志管理功能
        function refreshLogFiles() {
            fetch('/api/log-files')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayLogFiles(data.files || []);
                    } else {
                        document.getElementById('logFilesContainer').innerHTML = 
                            '<div class="logs-empty"><i class="fas fa-exclamation-triangle"></i> ' + data.message + '</div>';
                    }
                })
                .catch(error => {
                    console.error('获取日志文件失败:', error);
                    document.getElementById('logFilesContainer').innerHTML = 
                        '<div class="logs-empty"><i class="fas fa-exclamation-triangle"></i> 获取日志文件失败</div>';
                });
        }
        
        function setLogLevel() {
            const level = document.getElementById('logLevelSelect').value;
            
            fetch('/api/logs', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ logLevel: level })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(data.message, 'success');
                } else {
                    showAlert('设置日志等级失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('设置日志等级失败:', error);
                showAlert('设置日志等级失败: ' + error.message, 'error');
            });
        }
        
        function displayLogFiles(files) {
            const container = document.getElementById('logFilesContainer');
            
            if (!container) {
                console.error('找不到logFilesContainer元素');
                return;
            }
            
            if (files.length === 0) {
                container.innerHTML = '<div class="logs-empty"><i class="fas fa-info-circle"></i> 暂无日志文件</div>';
                return;
            }
            
            const filesHtml = files.map((file, index) => {
                const sizeStr = formatBytes(file.size);
                const safeName = escapeHtml(file.name);
                const itemId = 'log-file-' + index;
                return '<div class="log-file-item" id="' + itemId + '" data-filename="' + escapeHtml(file.name) + '" title="点击下载 ' + safeName + '">' +
                    '<div class="log-file-info">' +
                        '<div class="log-file-name"><i class="fas fa-file-alt"></i> ' + safeName + '</div>' +
                        '<div class="log-file-details">' +
                            '<span class="log-file-size">' + sizeStr + '</span>' +
                            '<span class="log-file-date">' + file.modified + '</span>' +
                        '</div>' +
                    '</div>' +
                    '<div class="log-file-download-icon">' +
                        '<i class="fas fa-download"></i>' +
                    '</div>' +
                '</div>';
            }).join('');
            
            container.innerHTML = filesHtml;
            
            // 为每个文件项添加点击事件监听器
            files.forEach((file, index) => {
                const itemId = 'log-file-' + index;
                const element = document.getElementById(itemId);
                if (element) {
                    element.addEventListener('click', function() {
                        downloadLogFile(file.name);
                    });
                }
            });
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        function downloadLogFile(fileName) {
            if (!fileName) {
                showAlert('文件名错误', 'error');
                return;
            }
            
            const url = '/api/download-log?file=' + encodeURIComponent(fileName);
            
            try {
                const a = document.createElement('a');
                a.href = url;
                a.download = fileName;
                a.target = '_blank';
                a.style.display = 'none';
                
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                
                showAlert('开始下载: ' + fileName, 'success');
            } catch (error) {
                console.error('下载过程出错:', error);
                showAlert('下载失败: ' + error.message, 'error');
            }
        }
        
        // 日志配置相关函数
        function showLogConfigModal() {
            const modal = document.getElementById('logConfigModal');
            loadLogConfig();
            modal.style.display = 'flex';
        }
        
        function hideLogConfigModal() {
            const modal = document.getElementById('logConfigModal');
            modal.style.display = 'none';
        }
        
        function loadLogConfig() {
            fetch('/api/log-config')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('maxLogSizeInput').value = data.config.maxLogSize;
                        document.getElementById('maxLogFilesInput').value = data.config.maxLogFiles;
                        document.getElementById('rotateByTimeCheck').checked = data.config.rotateByTime;
                        document.getElementById('rotateIntervalInput').value = data.config.rotateInterval;
                        toggleTimeRotate();
                        // 保存原始配置
                        originalConfig = {
                            maxLogSize: data.config.maxLogSize,
                            maxLogFiles: data.config.maxLogFiles,
                            rotateByTime: data.config.rotateByTime,
                            rotateInterval: data.config.rotateInterval
                        };
                    }
                })
                .catch(error => {
                    console.error('获取日志配置失败:', error);
                });
        }
        
        function toggleTimeRotate() {
            const checked = document.getElementById('rotateByTimeCheck').checked;
            const group = document.getElementById('timeRotateGroup');
            group.style.display = checked ? 'block' : 'none';
        }
        
        let originalConfig = {};
        
        function applyLogConfig() {
            const config = {
                maxLogSize: parseInt(document.getElementById('maxLogSizeInput').value),
                maxLogFiles: parseInt(document.getElementById('maxLogFilesInput').value),
                rotateByTime: document.getElementById('rotateByTimeCheck').checked,
                rotateInterval: parseInt(document.getElementById('rotateIntervalInput').value)
            };
            
            fetch('/api/log-config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('日志配置已应用', 'success', 2000);
                    // 更新原始配置
                    originalConfig = { ...config };
                    // 关闭弹窗
                    hideLogConfigModal();
                } else {
                    showAlert('配置应用失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('应用日志配置失败:', error);
                showAlert('配置应用失败: ' + error.message, 'error');
            });
        }
        
        function resetLogConfig() {
            if (originalConfig.maxLogSize !== undefined) {
                document.getElementById('maxLogSizeInput').value = originalConfig.maxLogSize;
                document.getElementById('maxLogFilesInput').value = originalConfig.maxLogFiles;
                document.getElementById('rotateByTimeCheck').checked = originalConfig.rotateByTime;
                document.getElementById('rotateIntervalInput').value = originalConfig.rotateInterval;
                toggleTimeRotate();
                showAlert('配置已重置', 'info', 1500);
            }
        }
        

        
        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            if (statusUpdateInterval) {
                clearInterval(statusUpdateInterval);
            }
        });
    </script>
    
    <!-- 日志配置弹窗 -->
    <div id="logConfigModal" class="config-modal" style="display: none;">
        <div class="config-modal-overlay" onclick="hideLogConfigModal()"></div>
        <div class="config-modal-content">
            <div class="config-modal-header">
                <h3><i class="fas fa-sliders-h"></i> 日志配置</h3>
                <button class="config-modal-close" onclick="hideLogConfigModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="config-modal-body">
                <div class="config-item">
                    <label for="logLevelSelect">日志等级:</label>
                    <select id="logLevelSelect" onchange="setLogLevel()">
                        <option value="DEBUG">DEBUG</option>
                        <option value="INFO">INFO</option>
                        <option value="WARN">WARN</option>
                        <option value="ERROR" selected>ERROR</option>
                    </select>
                </div>
                <div class="config-item">
                    <label for="maxLogSizeInput">文件大小 (MB):</label>
                    <input type="number" id="maxLogSizeInput" min="1" max="1000" value="10">
                </div>
                <div class="config-item">
                    <label for="maxLogFilesInput">保留文件数:</label>
                    <input type="number" id="maxLogFilesInput" min="1" max="100" value="10">
                </div>
                <div class="config-item">
                    <label>
                        <input type="checkbox" id="rotateByTimeCheck" onchange="toggleTimeRotate();">
                        按时间轮转
                    </label>
                </div>
                <div class="config-item" id="timeRotateGroup" style="display: none;">
                    <label for="rotateIntervalInput">轮转间隔 (小时):</label>
                    <input type="number" id="rotateIntervalInput" min="1" max="168" value="24">
                </div>
            </div>
            <div class="config-modal-footer">
                <button class="btn-apply" onclick="applyLogConfig()">
                    <i class="fas fa-check"></i> 应用配置
                </button>
                <button class="btn-reset" onclick="resetLogConfig()">
                    <i class="fas fa-undo"></i> 重置
                </button>
                <button class="btn-cancel" onclick="hideLogConfigModal()">
                    <i class="fas fa-times"></i> 取消
                </button>
            </div>
        </div>
    </div>
    
    <!-- 通知容器 -->
    <div id="notificationContainer" class="notification-container"></div>
</body>
</html>
`

// webConfigHandler 处理配置页面请求
func webConfigHandler(w http.ResponseWriter, r *http.Request) {
	tmpl, err := template.New("config").Parse(webTemplate)
	if err != nil {
		http.Error(w, "模板解析错误", http.StatusInternalServerError)
		return
	}

	err = tmpl.Execute(w, config)
	if err != nil {
		http.Error(w, "模板执行错误", http.StatusInternalServerError)
		return
	}
}

// apiConfigHandler 处理配置API请求
func apiConfigHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	if r.Method == "GET" {
		json.NewEncoder(w).Encode(config)
		return
	}

	if r.Method == "POST" {
		var newConfig Config
		if err := json.NewDecoder(r.Body).Decode(&newConfig); err != nil {
			json.NewEncoder(w).Encode(map[string]interface{}{
				"success": false,
				"message": "配置数据格式错误: " + err.Error(),
			})
			return
		}

		// 保存配置到文件
		if err := saveConfig(configFilePath, newConfig); err != nil {
			json.NewEncoder(w).Encode(map[string]interface{}{
				"success": false,
				"message": "保存配置文件失败: " + err.Error(),
			})
			return
		}

		// 更新内存中的配置
		config = newConfig

		json.NewEncoder(w).Encode(map[string]interface{}{
			"success": true,
			"message": "配置保存成功，请重启服务以应用新配置",
		})
		return
	}

	http.Error(w, "不支持的请求方法", http.StatusMethodNotAllowed)
}

// apiRestartHandler 处理重启API请求
func apiRestartHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	if r.Method != "POST" {
		http.Error(w, "不支持的请求方法", http.StatusMethodNotAllowed)
		return
	}

	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"message": "正在重启服务，请稍候...",
	})

	// 延迟重启，让响应先发送
	go func() {
		defer recoverFromPanic("Web重启处理")
		time.Sleep(1 * time.Second)
		addLogWithLevel("INFO", "通过Web界面请求重启服务...")

		// 优雅关闭当前进程的所有服务
		addLogWithLevel("INFO", "正在关闭当前服务...")
		running = false

		// 关闭Web服务器
		if webServer != nil {
			ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
			defer cancel()
			webServer.Shutdown(ctx)
		}

		// 关闭TCP服务器
		if serverSock != nil {
			serverSock.Close()
		}

		// 关闭MQTT连接
		if mqttClient != nil {
			mqttClient.Disconnect(250)
		}

		// 等待所有goroutines退出，但设置超时
		addLogWithLevel("INFO", "等待所有协程退出...")
		done := make(chan bool, 1)
		go func() {
			wg.Wait()
			done <- true
		}()

		select {
		case <-done:
			addLogWithLevel("INFO", "所有协程已正常退出")
		case <-time.After(5 * time.Second):
			addLogWithLevel("WARN", "等待协程退出超时，强制继续")
		}

		// 在所有协程退出后再关闭队列
		if queue != nil {
			close(queue)
		}

		// 关闭串口
		if masterFd != nil {
			masterFd.Close()
		}
		if slaveFd != nil {
			slaveFd.Close()
		}

		// 等待端口完全释放
		time.Sleep(1 * time.Second)

		// 使用简单的后台启动方式
		executable, err := os.Executable()
		if err != nil {
			addLogWithLevel("ERROR", fmt.Sprintf("获取程序路径失败: %v", err))
			os.Exit(1)
		}

		// 构建启动命令
		args := []string{executable}
		args = append(args, os.Args[1:]...)

		// 使用bash -c来启动新进程，确保完全独立
		cmdStr := ""
		for i, arg := range args {
			if i > 0 {
				cmdStr += " "
			}
			cmdStr += `"` + arg + `"`
		}

		addLogWithLevel("INFO", fmt.Sprintf("启动新进程: %s", cmdStr))

		// 直接启动新进程，不使用bash包装
		cmd := exec.Command(executable, os.Args[1:]...)
		cmd.Env = os.Environ()

		// 让新进程在后台运行，但不脱离终端
		err = cmd.Start()
		if err != nil {
			addLogWithLevel("ERROR", fmt.Sprintf("启动新进程失败: %v", err))
			os.Exit(1)
		}

		addLogWithLevel("INFO", fmt.Sprintf("新进程启动成功，PID: %d，当前进程退出", cmd.Process.Pid))
		os.Exit(0)
	}()
}

// apiStatusHandler 处理状态API请求
func apiStatusHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	if r.Method != "GET" {
		http.Error(w, "不支持的请求方法", http.StatusMethodNotAllowed)
		return
	}

	updateSystemStatus()
	status := getSystemStatus()

	json.NewEncoder(w).Encode(status)
}

// getLogFiles 获取日志文件列表
func getLogFiles() ([]map[string]interface{}, error) {
	files, err := filepath.Glob(filepath.Join(logDir, "*.log"))
	if err != nil {
		return nil, err
	}

	var logFiles []map[string]interface{}
	for _, file := range files {
		stat, err := os.Stat(file)
		if err != nil {
			continue
		}

		fileName := filepath.Base(file)
		logFiles = append(logFiles, map[string]interface{}{
			"name":     fileName,
			"size":     stat.Size(),
			"modified": stat.ModTime().Format("2006-01-02 15:04:05"),
		})
	}

	// 按修改时间排序，最新的在前
	sort.Slice(logFiles, func(i, j int) bool {
		return logFiles[i]["modified"].(string) > logFiles[j]["modified"].(string)
	})

	return logFiles, nil
}

// apiLogFilesHandler 处理日志文件API请求
func apiLogFilesHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	if r.Method != "GET" {
		http.Error(w, "不支持的请求方法", http.StatusMethodNotAllowed)
		return
	}

	files, err := getLogFiles()
	if err != nil {
		json.NewEncoder(w).Encode(map[string]interface{}{
			"success": false,
			"message": fmt.Sprintf("获取日志文件列表失败: %v", err),
		})
		return
	}

	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"files":   files,
	})
}

// apiDownloadLogHandler 处理日志文件下载请求
func apiDownloadLogHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != "GET" {
		http.Error(w, "不支持的请求方法", http.StatusMethodNotAllowed)
		return
	}

	fileName := r.URL.Query().Get("file")
	if fileName == "" {
		http.Error(w, "缺少文件名参数", http.StatusBadRequest)
		return
	}

	// 安全检查，防止路径遍历攻击
	if strings.Contains(fileName, "..") || strings.Contains(fileName, "/") || strings.Contains(fileName, "\\") {
		http.Error(w, "无效的文件名", http.StatusBadRequest)
		return
	}

	filePath := filepath.Join(logDir, fileName)
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		http.Error(w, "文件不存在", http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/octet-stream")
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", fileName))

	http.ServeFile(w, r, filePath)
	addLogWithLevel("INFO", fmt.Sprintf("日志文件下载: %s", fileName))
}

// apiLogsHandler 处理日志API请求
func apiLogsHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	switch r.Method {
	case "POST":
		// 设置日志等级
		var req struct {
			LogLevel string `json:"logLevel"`
		}
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			json.NewEncoder(w).Encode(map[string]interface{}{
				"success": false,
				"message": "请求格式错误",
			})
			return
		}

		// 验证日志等级
		validLevels := []string{"DEBUG", "INFO", "WARN", "ERROR"}
		valid := false
		for _, level := range validLevels {
			if req.LogLevel == level {
				valid = true
				break
			}
		}

		if !valid {
			json.NewEncoder(w).Encode(map[string]interface{}{
				"success": false,
				"message": "无效的日志等级，支持: DEBUG, INFO, WARN, ERROR",
			})
			return
		}

		logLevel = req.LogLevel
		addLogWithLevel("INFO", fmt.Sprintf("日志等级已设置为: %s", logLevel))

		json.NewEncoder(w).Encode(map[string]interface{}{
			"success": true,
			"message": fmt.Sprintf("日志等级已设置为: %s", logLevel),
		})
	default:
		http.Error(w, "不支持的请求方法", http.StatusMethodNotAllowed)
	}
}

// apiLogConfigHandler 处理日志配置API请求
func apiLogConfigHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	switch r.Method {
	case "GET":
		// 获取当前日志配置
		json.NewEncoder(w).Encode(map[string]interface{}{
			"success": true,
			"config": map[string]interface{}{
				"maxLogSize":     maxLogSize / (1024 * 1024), // 转换为MB
				"maxLogFiles":    maxLogFiles,
				"rotateByTime":   logRotateByTime,
				"rotateInterval": logRotateInterval,
			},
		})
	case "POST":
		// 设置日志配置
		var req struct {
			MaxLogSize     int  `json:"maxLogSize"`
			MaxLogFiles    int  `json:"maxLogFiles"`
			RotateByTime   bool `json:"rotateByTime"`
			RotateInterval int  `json:"rotateInterval"`
		}
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			json.NewEncoder(w).Encode(map[string]interface{}{
				"success": false,
				"message": "请求格式错误",
			})
			return
		}

		// 验证参数
		if req.MaxLogSize < 1 || req.MaxLogSize > 1000 {
			json.NewEncoder(w).Encode(map[string]interface{}{
				"success": false,
				"message": "文件大小必须在1-1000MB之间",
			})
			return
		}

		if req.MaxLogFiles < 1 || req.MaxLogFiles > 100 {
			json.NewEncoder(w).Encode(map[string]interface{}{
				"success": false,
				"message": "保留文件数量必须在1-100之间",
			})
			return
		}

		if req.RotateByTime && (req.RotateInterval < 1 || req.RotateInterval > 168) {
			json.NewEncoder(w).Encode(map[string]interface{}{
				"success": false,
				"message": "轮转间隔必须在1-168小时之间",
			})
			return
		}

		// 更新配置
		maxLogSize = int64(req.MaxLogSize) * 1024 * 1024 // 转换为字节
		maxLogFiles = req.MaxLogFiles
		logRotateByTime = req.RotateByTime
		logRotateInterval = req.RotateInterval

		addLogWithLevel("INFO", fmt.Sprintf("日志轮转配置已更新: 大小=%dMB, 文件数=%d, 按时间轮转=%v, 间隔=%d小时",
			req.MaxLogSize, req.MaxLogFiles, req.RotateByTime, req.RotateInterval))

		json.NewEncoder(w).Encode(map[string]interface{}{
			"success": true,
			"message": "日志配置保存成功",
		})
	default:
		http.Error(w, "不支持的请求方法", http.StatusMethodNotAllowed)
	}
}

// startWebServer 启动Web服务器
func startWebServer() {
	http.HandleFunc("/", webConfigHandler)
	http.HandleFunc("/api/config", apiConfigHandler)
	http.HandleFunc("/api/status", apiStatusHandler)
	http.HandleFunc("/api/restart", apiRestartHandler)
	http.HandleFunc("/api/logs", apiLogsHandler)
	http.HandleFunc("/api/log-files", apiLogFilesHandler)
	http.HandleFunc("/api/download-log", apiDownloadLogHandler)
	http.HandleFunc("/api/log-config", apiLogConfigHandler)

	addLogWithLevel("INFO", fmt.Sprintf("Web配置界面启动在端口 %d", config.Network.WebPort))
	addLogWithLevel("INFO", fmt.Sprintf("访问地址: http://localhost:%d", config.Network.WebPort))

	webServer = &http.Server{
		Addr:    fmt.Sprintf(":%d", config.Network.WebPort),
		Handler: nil,
	}

	go func() {
		defer recoverFromPanic("Web服务器")
		if err := webServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			addLogWithLevel("ERROR", fmt.Sprintf("Web服务器启动失败: %v", err))
		}
	}()
}

func main() {
	// 初始化日志系统
	if err := initLogSystem(); err != nil {
		log.Fatalf("初始化日志系统失败: %v", err)
	}
	defer closeLogSystem()

	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 初始化启动时间
	startTime = time.Now()

	if err := loadConfig(); err != nil {
		addLogWithLevel("ERROR", fmt.Sprintf("加载配置失败: %v", err))
		log.Fatal(err)
	}
	addLogWithLevel("INFO", fmt.Sprintf("配置加载成功，配置文件: %s", configFilePath))
	addLogWithLevel("INFO", fmt.Sprintf("串口配置 - 主站: %s, 从站: %s, 波特率: %d",
		config.Serial.MasterDevice, config.Serial.SlaveDevice, config.Serial.BaudRate))
	addLogWithLevel("INFO", fmt.Sprintf("网络配置 - TCP端口: %d, Web端口: %d, MQTT: %s",
		config.Network.TCPPort, config.Network.WebPort, config.Network.MqttBroker))

	var err error
	masterFd, err = initSerial(config.Serial.MasterDevice)
	if err != nil {
		log.Fatal(err)
	}
	defer masterFd.Close()

	slaveFd, err = initSerial(config.Serial.SlaveDevice)
	if err != nil {
		log.Fatal(err)
	}
	defer slaveFd.Close()

	serverSock, err = initTCPServer(config.Network.TCPPort)
	if err != nil {
		addLogWithLevel("ERROR", fmt.Sprintf("TCP服务器启动失败: %v", err))
		log.Fatal(err)
	}
	defer serverSock.Close()
	addLogWithLevel("INFO", fmt.Sprintf("TCP服务器启动成功，监听端口: %d", config.Network.TCPPort))

	queue = make(chan Request, config.Runtime.QueueSize)
	pool = sync.Pool{New: func() interface{} { return make([]byte, config.Modbus.MaxADULength) }}
	addLogWithLevel("INFO", fmt.Sprintf("请求队列初始化完成，队列大小: %d", config.Runtime.QueueSize))

	// 启动Web服务器
	startWebServer()

	// 启动状态监控
	startStatusMonitor()
	addLogWithLevel("INFO", "系统状态监控已启动")

	wg.Add(4) // worker + tcpListener + rtuListener + mqttListener
	addLogWithLevel("INFO", "正在启动工作协程...")
	go worker(0)
	go tcpListener()
	go rtuListener()
	go mqttListener()

	addLogWithLevel("INFO", "Modbus网关启动成功")
	addLogWithLevel("INFO", fmt.Sprintf("TCP端口: %d", config.Network.TCPPort))
	addLogWithLevel("INFO", fmt.Sprintf("Web管理端口: %d", config.Network.WebPort))
	addLogWithLevel("INFO", fmt.Sprintf("设备ID: %s", config.Network.DeviceID))

	// 添加启动日志
	addLogWithLevel("INFO", "====================================")
	addLogWithLevel("INFO", "Modbus网关启动成功 - 所有服务已就绪")
	addLogWithLevel("INFO", fmt.Sprintf("TCP服务端口: %d (Modbus TCP协议)", config.Network.TCPPort))
	addLogWithLevel("INFO", fmt.Sprintf("Web管理端口: %d (HTTP管理界面)", config.Network.WebPort))
	addLogWithLevel("INFO", fmt.Sprintf("设备标识: %s", config.Network.DeviceID))
	addLogWithLevel("INFO", fmt.Sprintf("MQTT连接: %s", config.Network.MqttBroker))
	addLogWithLevel("INFO", "系统已进入运行状态，等待请求...")

	<-sigChan
	fmt.Println("\n收到终止信号，正在退出...")
	addLogWithLevel("INFO", "收到系统终止信号，开始关闭所有服务...")
	running = false

	// 关闭Web服务器
	if webServer != nil {
		addLogWithLevel("INFO", "正在关闭Web服务器...")
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		webServer.Shutdown(ctx)
		addLogWithLevel("INFO", "Web服务器已关闭")
	}

	// 关闭TCP服务器
	addLogWithLevel("INFO", "正在关闭TCP服务器...")
	serverSock.Close()
	addLogWithLevel("INFO", "TCP服务器已关闭")

	// 关闭MQTT连接
	if mqttClient != nil {
		addLogWithLevel("INFO", "正在断开MQTT连接...")
		mqttClient.Disconnect(250)
		addLogWithLevel("INFO", "MQTT连接已断开")
	}

	// 等待所有goroutines退出
	addLogWithLevel("INFO", "等待所有工作协程退出...")
	wg.Wait()

	// 在所有协程退出后再关闭队列
	addLogWithLevel("INFO", "正在关闭请求队列...")
	close(queue)
	addLogWithLevel("INFO", "====================================")
	addLogWithLevel("INFO", "Modbus网关已完全关闭")
	addLogWithLevel("INFO", "程序已退出")
}
