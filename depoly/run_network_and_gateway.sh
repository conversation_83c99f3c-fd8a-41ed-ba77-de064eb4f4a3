#!/bin/bash

# Set variables
TIMEOUT=60
INTERVAL=5
ELAPSED=0
MAX_RETRIES=5  # Number of retry attempts for the full sequence
DHCLIENT_DELAY=5  # Delay between retries
NOHUP_COMMAND="/root/modbus-gateway/gateway_web /root/modbus-gateway/config_web.yaml"

# # Wait for usb0 device to come online, up to 60 seconds
# while [ $ELAPSED -lt $TIMEOUT ]; do
#     ip link show usb0 > /dev/null 2>&1
#     if [ $? -eq 0 ]; then
#         echo "usb0 device is online."
#         break  # Successfully exit the loop
#     else
#         echo "usb0 device not detected... Waited $ELAPSED seconds"
#         sleep $INTERVAL
#         if [ $? -ne 0 ]; then  # Check if sleep failed
#             echo "Error: Sleep command failed."
#             exit 1
#         fi
#         ELAPSED=$((ELAPSED + INTERVAL))
#     fi
#     if [ $ELAPSED -ge $TIMEOUT ]; then
#         echo "Error: usb0 device did not come online within 60 seconds."
#         exit 1
#     fi
# done

# # Retry loop for the full sequence: ifconfig, AT command, and dhclient
# RETRY_COUNT=0
# while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
#     # Activate USB0 interface and check for success
#     ifconfig usb0 up
#     if [ $? -ne 0 ]; then
#         echo "Error: Failed to activate usb0 interface."
#         exit 1
#     fi
#     echo "Success: usb0 interface has been activated."

#     # Send AT command to /dev/ttyUSB1 and check for success
#     if echo -e "AT^NDISDUP=1,1\r\n" > /dev/ttyUSB1; then
#         echo "Success: AT command has been sent to /dev/ttyUSB1."
#     else
#         echo "Error: Sending AT command failed, possibly due to permissions or device issues."
#         exit 1
#     fi

#     # Run dhclient to get IP address
#     dhclient usb0
#     if [ $? -ne 0 ]; then
#         echo "Warning: dhclient usb0 command failed initially. Retrying sequence."
#     else
#         echo "dhclient usb0 executed."
#     fi

#     # Check if usb0 has an IP address
#     if ip addr show usb0 | grep "inet"; then
#         echo "Success: usb0 has an IP address assigned."
#         break  # IP is present, exit the loop
#     else
#         echo "Warning: usb0 does not have an IP address. Retrying full sequence."
#         RETRY_COUNT=$((RETRY_COUNT + 1))
#         if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
#             echo "Retrying... Waiting $DHCLIENT_DELAY seconds."
#             sleep $DHCLIENT_DELAY
#             if [ $? -ne 0 ]; then  # Check if sleep failed
#                 echo "Error: Sleep command failed."
#                 exit 1
#             fi
#         else
#             echo "Error: Failed to assign IP to usb0 after $MAX_RETRIES attempts."
#             exit 1  # Final failure, exit the script
#         fi
#     fi
# done

# Run modbus_gateway in the background and check if it started successfully
nohup $NOHUP_COMMAND > /dev/null 2>&1 &
if [ $? -eq 0 ]; then
    echo "Success: modbus_gateway has been started in the background."
else
    echo "Error: Failed to start modbus_gateway, possibly due to command or file issues."
    exit 1
fi
