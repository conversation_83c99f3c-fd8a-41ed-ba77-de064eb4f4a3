#!/bin/bash

# Modbus网关远程部署脚本
# 适用于RK3566设备的交叉编译和SCP部署

# ===== 配置区域 - 请根据实际情况修改 =====
TARGET_HOST="**************"              # 目标RK3566设备IP
TARGET_USER="root"                        # 目标设备用户名  
TARGET_DIR="/root/modbus-gateway"         # 目标部署目录
MODBUS_PORT="1502"                        # Modbus TCP端口
WEB_PORT="8080"                           # Web管理端口
CONFIG_FILE="config_web.yaml"             # 配置文件名
BINARY_NAME="gateway_web"                 # 编译后的二进制文件名
# ==========================================

set -e

echo "🚀 Modbus网关远程部署系统"
echo "构建环境: $(uname -m)"
echo "目标环境: ARMv6 (RK3566)"
echo "目标主机: $TARGET_USER@$TARGET_HOST:$TARGET_DIR"
echo ""

# 检查参数
if [ $# -eq 0 ]; then
    echo "用法:"
    echo "  $0 build         # 仅编译网关程序"
    echo "  $0 deploy [5g]   # 编译并部署网关到远程设备（可选5g参数）"
    echo "  $0 start         # 启动远程网关服务"
    echo "  $0 stop          # 停止远程网关服务"
    echo "  $0 restart       # 重启远程网关服务"
    echo "  $0 status        # 查看服务状态"
    echo "  $0 logs          # 查看远程网关日志"
    echo "  $0 config        # 同步配置文件"
    echo "  $0 web           # 打开Web管理界面"
    echo "  $0 all [5g]      # 完整流程：编译+部署+启动（可选5g参数）"
    echo ""
    echo "示例:"
    echo "  $0 all           # 一键部署并启动（使用标准网络脚本）"
    echo "  $0 all 5g        # 一键部署并启动（使用5G网络脚本）"
    echo "  $0 deploy 5g     # 部署5G版本网络脚本"
    echo "  $0 logs          # 查看运行日志"
    echo "  $0 web           # 打开Web界面"
    exit 1
fi

# 检查SSH连接
check_ssh() {
    echo "🔗 检查SSH连接..."
    if ssh -o ConnectTimeout=5 "$TARGET_USER@$TARGET_HOST" "echo '连接成功'" 2>/dev/null; then
        echo "✅ SSH连接正常"
    else
        echo "❌ SSH连接失败，请检查:"
        echo "   - IP地址: $TARGET_HOST"
        echo "   - 用户名: $TARGET_USER"
        echo "   - SSH密钥或密码配置"
        echo "   - 设备是否开机并连接网络"
        exit 1
    fi
}

# 检查必需文件
check_files() {
    echo "📁 检查必需文件..."
    
    if [ ! -f "gateway_web.go" ]; then
        echo "❌ 未找到源文件: gateway_web.go"
        exit 1
    fi
    
    if [ ! -f "$CONFIG_FILE" ]; then
        echo "❌ 未找到配置文件: $CONFIG_FILE"
        echo "   请确保存在配置文件，或修改脚本中的CONFIG_FILE变量"
        exit 1
    fi
    
    if [ ! -f "go.mod" ]; then
        echo "❌ 未找到 go.mod 文件"
        exit 1
    fi
    
    echo "✅ 文件检查通过"
}

# 交叉编译
build_gateway() {
    echo ""
    echo "📦 交叉编译Modbus网关 (ARMv6)..."
    
    check_files
    
    # 设置Go模块代理（如果需要）
    export GOPROXY=https://goproxy.cn,direct
    
    # 交叉编译到ARMv6 (RK3566)
    echo "   编译目标: linux/armv6"
    GOOS=linux GOARCH=arm GOARM=6 CGO_ENABLED=0  go build -ldflags="-s -w" -o "${BINARY_NAME}_armv6" gateway_web.go
    
    if [ $? -eq 0 ]; then
        echo "✅ 编译成功"
        ls -lh "${BINARY_NAME}_armv6"
        file "${BINARY_NAME}_armv6"
    else
        echo "❌ 编译失败"
        exit 1
    fi
}

# 部署文件
deploy_files() {
    echo ""
    echo "📤 部署文件到远程RK3566设备..."
    
    # 停止现有服务 (兼容buildroot)
    echo "   停止现有服务..."
    ssh "$TARGET_USER@$TARGET_HOST" "ps | grep $BINARY_NAME | grep -v grep | awk '{print \$1}' | xargs kill -9 2>/dev/null || true"
    
    # 创建目录
    echo "   创建目录..."
    ssh "$TARGET_USER@$TARGET_HOST" "mkdir -p $TARGET_DIR"
    ssh "$TARGET_USER@$TARGET_HOST" "mkdir -p /userdata"
    
    # 复制二进制文件
    echo "   上传二进制文件..."
    scp "${BINARY_NAME}_armv6" "$TARGET_USER@$TARGET_HOST:$TARGET_DIR/$BINARY_NAME"
    
    # 复制配置文件
    echo "   上传配置文件..."
    scp "$CONFIG_FILE" "$TARGET_USER@$TARGET_HOST:$TARGET_DIR/"
    
    # 根据参数选择并部署网络启动脚本
    echo "   部署网络启动脚本..."
    scp "depoly/autorun.sh" "$TARGET_USER@$TARGET_HOST:/userdata/autorun.sh"
    
    if [[ "$*" =~ "5g" ]]; then
        # 使用5G版本的网络脚本
        if [ -f "depoly/run_network_and_gateway-5g.sh" ]; then
            echo "   使用5G网络脚本..."
            scp "depoly/run_network_and_gateway-5g.sh" "$TARGET_USER@$TARGET_HOST:/userdata/run_network_and_gateway.sh"
        else
            echo "   ⚠️  5G网络脚本文件不存在: depoly/run_network_and_gateway-5g.sh"
        fi
    else
        # 使用标准版本的网络脚本
        if [ -f "depoly/run_network_and_gateway.sh" ]; then
            echo "   使用标准网络脚本..."
            scp "depoly/run_network_and_gateway.sh" "$TARGET_USER@$TARGET_HOST:/userdata/run_network_and_gateway.sh"
        else
            echo "   ⚠️  标准网络脚本文件不存在: depoly/run_network_and_gateway.sh"
        fi
    fi
    
    # 复制其他必要文件（如果存在）
    if [ -f "README_DEPLOY.md" ]; then
        scp "README_DEPLOY.md" "$TARGET_USER@$TARGET_HOST:$TARGET_DIR/"
    fi
    
    # 设置权限
    echo "   设置文件权限..."
    ssh "$TARGET_USER@$TARGET_HOST" "
        chmod +x $TARGET_DIR/$BINARY_NAME
        chmod 644 $TARGET_DIR/$CONFIG_FILE
        chmod +x /userdata/run_network_and_gateway.sh 2>/dev/null || true
        chown -R $TARGET_USER:$TARGET_USER $TARGET_DIR
    "
    
    echo "✅ 文件部署完成"
}



# 启动服务
start_service() {
    echo ""
    echo "🚀 启动远程Modbus网关服务..."
    
    # 停止现有服务 (兼容buildroot)
    ssh "$TARGET_USER@$TARGET_HOST" "ps | grep $BINARY_NAME | grep -v grep | awk '{print \$1}' | xargs kill -9 2>/dev/null || true"
    sleep 1
    
    # 启动新服务 (改进PID检测)
    ssh "$TARGET_USER@$TARGET_HOST" "
        cd $TARGET_DIR
        nohup ./$BINARY_NAME $CONFIG_FILE > gateway.log 2>&1 &
        sleep 1
        # 查找实际的进程PID并写入文件
        ps | grep './$BINARY_NAME' | grep -v grep | awk '{print \$1}' > gateway.pid
        echo '服务已启动，PID: '\$(cat gateway.pid 2>/dev/null || echo '未找到')
    "
    
    sleep 3
    
    # 检查服务状态 (改进检测逻辑)
    if ssh "$TARGET_USER@$TARGET_HOST" "test -f $TARGET_DIR/gateway.pid && test -s $TARGET_DIR/gateway.pid && ps | grep './$BINARY_NAME' | grep -v grep > /dev/null"; then
        echo "✅ 网关服务启动成功"
        
        echo ""
        echo "📊 服务信息:"
        echo "   Modbus TCP: $TARGET_HOST:$MODBUS_PORT"
        echo "   Web管理界面: http://$TARGET_HOST:$WEB_PORT"
        echo "   日志文件: $TARGET_DIR/gateway.log"
        echo "   PID文件: $TARGET_DIR/gateway.pid"
        
    else
        echo "❌ 服务启动失败，查看日志:"
        ssh "$TARGET_USER@$TARGET_HOST" "cat $TARGET_DIR/gateway.log 2>/dev/null || echo '日志文件不存在'"
        exit 1
    fi
}

# 停止服务
stop_service() {
    echo ""
    echo "🛑 停止远程Modbus网关服务..."
    
    if ssh "$TARGET_USER@$TARGET_HOST" "test -f $TARGET_DIR/gateway.pid"; then
        ssh "$TARGET_USER@$TARGET_HOST" "kill \$(cat $TARGET_DIR/gateway.pid) 2>/dev/null || true"
        ssh "$TARGET_USER@$TARGET_HOST" "rm -f $TARGET_DIR/gateway.pid"
        echo "✅ 服务已停止"
    else
        echo "⚠️  未找到PID文件，尝试强制停止..."
        ssh "$TARGET_USER@$TARGET_HOST" "ps | grep $BINARY_NAME | grep -v grep | awk '{print \$1}' | xargs kill -9 2>/dev/null || true"
    fi
}

# 检查服务状态
check_service_status() {
    echo ""
    echo "📊 检查服务状态..."
    
    # 检查进程状态
    if ssh "$TARGET_USER@$TARGET_HOST" "ps | grep $BINARY_NAME | grep -v grep > /dev/null"; then
        ssh "$TARGET_USER@$TARGET_HOST" "ps | grep $BINARY_NAME | grep -v grep"
        echo "✅ 服务正在运行"
        
        echo ""
        echo "📊 服务信息:"
        echo "   Modbus TCP: $TARGET_HOST:$MODBUS_PORT"
        echo "   Web管理界面: http://$TARGET_HOST:$WEB_PORT"
        echo "   配置文件: $TARGET_DIR/$CONFIG_FILE"
        echo "   日志文件: $TARGET_DIR/gateway.log"
        echo "   PID文件: $TARGET_DIR/gateway.pid"
        
    else
        echo "❌ 服务未运行"
    fi
}

# 查看日志
show_logs() {
    echo ""
    echo "📋 远程网关日志 (最后20行):"
    echo "=================================="
    ssh "$TARGET_USER@$TARGET_HOST" "tail -20 $TARGET_DIR/gateway.log 2>/dev/null || echo '日志文件不存在'"
}

# 同步配置文件
sync_config() {
    echo ""
    echo "⚙️  同步配置文件..."
    
    if [ ! -f "$CONFIG_FILE" ]; then
        echo "❌ 本地配置文件 $CONFIG_FILE 不存在"
        exit 1
    fi
    
    # 备份远程配置
    ssh "$TARGET_USER@$TARGET_HOST" "
        if [ -f $TARGET_DIR/$CONFIG_FILE ]; then
            cp $TARGET_DIR/$CONFIG_FILE $TARGET_DIR/${CONFIG_FILE}.backup.\$(date +%Y%m%d_%H%M%S)
        fi
    "
    
    # 上传新配置
    scp "$CONFIG_FILE" "$TARGET_USER@$TARGET_HOST:$TARGET_DIR/"
    
    echo "✅ 配置文件同步完成"
    echo "💡 需要重启服务以使配置生效"
}

# 打开Web界面
open_web() {
    echo ""
    echo "🌐 打开Web管理界面..."
    echo "   URL: http://$TARGET_HOST:$WEB_PORT"
    
    # 尝试在不同系统中打开浏览器
    if command -v open >/dev/null; then
        # macOS
        open "http://$TARGET_HOST:$WEB_PORT"
    elif command -v xdg-open >/dev/null; then
        # Linux
        xdg-open "http://$TARGET_HOST:$WEB_PORT"
    elif command -v start >/dev/null; then
        # Windows
        start "http://$TARGET_HOST:$WEB_PORT"
    else
        echo "   请手动在浏览器中打开上述URL"
    fi
}

# 清理本地编译文件
cleanup() {
    echo ""
    echo "🧹 清理本地编译文件..."
    rm -f "${BINARY_NAME}_armv6"
    echo "✅ 清理完成"
}

# 主逻辑
case "$1" in
    "build")
        build_gateway
        ;;
        
    "deploy")
        check_ssh
        build_gateway
        deploy_files "$@"
        ;;
        
    "start") 
        check_ssh
        start_service
        ;;
        
    "stop")
        check_ssh  
        stop_service
        ;;
        
    "restart")
        check_ssh
        stop_service
        sleep 2
        start_service
        ;;
        
    "status")
        check_ssh
        check_service_status
        ;;
        
    "logs")
        check_ssh
        show_logs
        ;;
        
    "config")
        check_ssh
        sync_config
        ;;
        
    "web")
        open_web
        ;;
        
    "all")
        check_ssh
        build_gateway
        deploy_files "$@"
        start_service
        echo ""
        echo "⏱️  等待服务完全启动..."
        sleep 3
        ;;
        
    "clean")
        cleanup
        ;;
        
    *)
        echo "❌ 未知参数: $1"
        echo "支持的参数: build, deploy [5g], start, stop, restart, status, logs, config, web, all [5g], clean"
        exit 1
        ;;
esac

echo ""
echo "✅ 操作完成"

# 显示常用命令提示  
if [ "$1" != "logs" ] && [ "$1" != "web" ] && [ "$1" != "clean" ]; then
    echo ""
    echo "🔧 常用命令 (兼容buildroot):"
    echo "   查看实时日志: ssh $TARGET_USER@$TARGET_HOST 'tail -f $TARGET_DIR/gateway.log'"
    echo "   检查进程状态: ssh $TARGET_USER@$TARGET_HOST 'ps | grep $BINARY_NAME'"
    echo "   手动停止服务: ssh $TARGET_USER@$TARGET_HOST 'ps | grep $BINARY_NAME | grep -v grep | awk \"{print \\\$1}\" | xargs kill -9'"
    echo "   重启服务: $0 stop && $0 start"
    echo "   Web界面: http://$TARGET_HOST:$WEB_PORT"
    echo "   网络脚本位置: /userdata/run_network_and_gateway.sh"
    
    # 显示当前部署的网络脚本类型
    if [[ "$*" =~ "5g" ]]; then
        echo "   当前部署: 5G网络脚本版本"
    else
        echo "   当前部署: 标准网络脚本版本"
    fi
fi 