#!/bin/bash

# 快速部署脚本 - Mac M1 到 ARM v6
# 使用前请修改下面的配置

# ===== 配置区域 - 请根据实际情况修改 =====
TARGET_HOST="**************"    # 目标机器IP
TARGET_USER="root"             # 目标机器用户名
TARGET_DIR="/root/gateway"      # 目标部署目录
# ==========================================

set -e

echo "🚀 Modbus网关快速部署"
echo "构建环境: Mac M1"
echo "目标环境: ARM v6 (RK3566)"
echo "目标主机: $TARGET_USER@$TARGET_HOST:$TARGET_DIR"
echo ""

# 1. 交叉编译
echo "📦 交叉编译中..."
GOOS=linux GOARCH=arm GOARM=6 CGO_ENABLED=0 go build -ldflags="-s -w" -o gateway_web_armv6 gateway_web.go

if [ $? -eq 0 ]; then
    echo "✅ 编译成功"
    ls -lh gateway_web_armv6
else
    echo "❌ 编译失败"
    exit 1
fi

# 2. 测试SSH连接
echo ""
echo "🔗 测试SSH连接..."
if ssh -o ConnectTimeout=5 "$TARGET_USER@$TARGET_HOST" "echo '连接成功'" 2>/dev/null; then
    echo "✅ SSH连接正常"
else
    echo "❌ SSH连接失败，请检查:"
    echo "   - IP地址: $TARGET_HOST"
    echo "   - 用户名: $TARGET_USER"
    echo "   - SSH密钥配置"
    exit 1
fi

# 3. 部署文件
echo ""
echo "📤 部署文件..."

# # 停止现有服务
# ssh "$TARGET_USER@$TARGET_HOST" "pkill -f gateway_web || true"

# 创建目录
ssh "$TARGET_USER@$TARGET_HOST" "mkdir -p $TARGET_DIR"

# 复制文件
scp gateway_web_armv6 "$TARGET_USER@$TARGET_HOST:$TARGET_DIR/"
scp config_web.yaml "$TARGET_USER@$TARGET_HOST:$TARGET_DIR/"

# 设置权限
ssh "$TARGET_USER@$TARGET_HOST" "chmod +x $TARGET_DIR/gateway_web_armv6"

echo "✅ 文件部署完成"

# # 4. 启动服务
# echo ""
# echo "🚀 启动服务..."

# ssh "$TARGET_USER@$TARGET_HOST" "cd $TARGET_DIR && nohup ./gateway_web_armv6 config_web.yaml > gateway.log 2>&1 & echo \$! > gateway.pid"

# sleep 2

# # 检查服务状态
# if ssh "$TARGET_USER@$TARGET_HOST" "ps -p \$(cat $TARGET_DIR/gateway.pid 2>/dev/null) > /dev/null 2>&1"; then
#     echo "✅ 服务启动成功"
    
#     # 获取目标机器IP
#     TARGET_IP=$(ssh "$TARGET_USER@$TARGET_HOST" "hostname -I | awk '{print \$1}'")
    
#     echo ""
#     echo "🎉 部署完成！"
#     echo "📊 服务信息:"
#     echo "   TCP端口: 1502"
#     echo "   Web管理: http://$TARGET_IP:8080"
#     echo "   日志文件: $TARGET_DIR/gateway.log"
#     echo ""
#     echo "🔧 管理命令:"
#     echo "   查看日志: ssh $TARGET_USER@$TARGET_HOST 'tail -f $TARGET_DIR/gateway.log'"
#     echo "   停止服务: ssh $TARGET_USER@$TARGET_HOST 'kill \$(cat $TARGET_DIR/gateway.pid)'"
#     echo "   重启服务: ssh $TARGET_USER@$TARGET_HOST 'cd $TARGET_DIR && kill \$(cat gateway.pid) && nohup ./gateway_web_armv6 config_web.yaml > gateway.log 2>&1 & echo \$! > gateway.pid'"
# else
#     echo "❌ 服务启动失败，请检查日志:"
#     echo "   ssh $TARGET_USER@$TARGET_HOST 'cat $TARGET_DIR/gateway.log'"
# fi 