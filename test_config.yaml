serial:
  baud_rate: 115200
  data_bits: 8
  parity: "N"
  stop_bits: 1
  timeout_ms: 1000
  mode: "RTU"

master:
  device: "/tmp/test_master"

slaves:
  - device: "/tmp/test_slave"
    slave_ids: [1, 2, 3]

network:
  tcp_port: 1502
  web_port: 8080
  max_clients: 5
  mqtt_broker: "tcp://*************:1883"
  device_id: "rk3566_001"
  mqtt_username: "kengque"
  mqtt_password: "Keng<PERSON>@nj"
  eth1_interface:
    use_dhcp: true
    ip_address: ""
    netmask: ""
    gateway: ""
    dns1: ""
    dns2: ""

modbus:
  max_adu_length: 256

runtime:
  queue_size: 500

auth:
  enable_auth: true
  session_timeout: 60  # 会话超时时间（分钟）
  users_file: "./users.yaml"  # 用户数据文件路径

users:
  - username: "admin"
    password_hash: "ef92b778bafe771e89245b89ecbc08a44a4e166c06659911881f383d4473e94f"  # Keng<PERSON>@2025的SHA256哈希
    created_at: "2025-01-01T00:00:00Z"
    last_login: "2025-01-01T00:00:00Z"
    is_active: true
